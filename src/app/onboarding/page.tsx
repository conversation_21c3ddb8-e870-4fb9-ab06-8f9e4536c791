"use client";

import { Onboarding } from "@/components/features/onboarding";
import { useSession } from "@/lib/auth-client";

export default function OnboardingPage() {
  const { data } = useSession();
  const user = data?.user;

  if (!user?.role) {
    return (
      <div className="flex h-full items-center justify-center">
        <div className="text-center">
          <p>Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 px-4 py-12">
      <Onboarding userRole={user.role as "homeowner" | "contractor"} />
    </div>
  );
}
