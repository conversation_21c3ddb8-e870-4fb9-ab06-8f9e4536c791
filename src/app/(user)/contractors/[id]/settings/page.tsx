"use client";

import { useQuery } from "@tanstack/react-query";
import { notFound, useParams } from "next/navigation";
import { PageLayout } from "@/components/core/page-layout";
import { ContractorSettings } from "@/components/features/contractors";
import { useTRPC } from "@/components/integrations/trpc/client";

export default function ContractorSettingsPage() {
  const trpc = useTRPC();
  const { id } = useParams<{ id: string }>();
  const { data: organization, isLoading } = useQuery(
    trpc.contractor.getById.queryOptions({ id }),
  );

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (!organization) {
    return notFound();
  }

  return (
    <PageLayout title="Contractor Settings">
      <div className="p-8">
        <ContractorSettings organizationId={id} />
      </div>
    </PageLayout>
  );
}
