import { type NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { auth } from "@/lib/auth";
import {
  generateFileKey,
  generatePresignedUrl,
  getFileExtension,
  isValidFileSize,
  isValidFileType,
  type UploadConfigType,
  uploadConfigs,
} from "@/lib/cloudflare-r2";

/**
 * Request schema for presigned URL generation
 */
const presignedUrlSchema = z.object({
  filename: z.string().min(1, "Filename is required"),
  contentType: z.string().min(1, "Content type is required"),
  fileSize: z.number().positive("File size must be positive"),
  uploadType: z
    .enum(["property-image", "job-image", "profile-image"])
    .default("job-image"),
});

export async function POST(request: NextRequest) {
  try {
    // Get the authenticated session
    const session = await auth.api.getSession({ headers: request.headers });

    if (!session?.user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    // Parse and validate the request body
    const body = await request.json();
    const validatedData = presignedUrlSchema.parse(body);

    const { filename, contentType, fileSize, uploadType } = validatedData;
    const userId = session.user.id;

    // Get upload configuration for the specified type
    const config = uploadConfigs[uploadType as UploadConfigType];

    // Validate file type
    if (!isValidFileType(contentType, [...config.allowedTypes])) {
      return NextResponse.json(
        {
          error: "Invalid file type",
          allowedTypes: config.allowedTypes,
        },
        { status: 400 },
      );
    }

    // Validate file size
    if (!isValidFileSize(fileSize, config.maxFileSize)) {
      return NextResponse.json(
        {
          error: "File size too large",
          maxSize: config.maxFileSize,
        },
        { status: 400 },
      );
    }

    // Generate file extension and key
    const extension = getFileExtension(filename, contentType);
    const key = generateFileKey({
      userId,
      type: uploadType,
      extension,
    });

    // Generate presigned URL
    const presignedUrl = await generatePresignedUrl({
      key,
      contentType,
      expiresIn: config.expiresIn,
      maxFileSize: config.maxFileSize,
    });

    // Return the presigned URL and file information
    return NextResponse.json({
      presignedUrl,
      key,
      publicUrl: `${process.env.NEXT_PUBLIC_STORAGE_URL}/${key}`,
      expiresIn: config.expiresIn,
    });
  } catch (error) {
    console.error("Error generating presigned URL:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: "Invalid request data",
          details: error.errors,
        },
        { status: 400 },
      );
    }

    return NextResponse.json(
      { error: "Failed to generate presigned URL" },
      { status: 500 },
    );
  }
}

/**
 * GET endpoint to retrieve upload configuration
 */
export async function GET(request: NextRequest) {
  try {
    // Get the authenticated session
    const session = await auth.api.getSession({ headers: request.headers });

    if (!session?.user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    const { searchParams } = new URL(request.url);
    const uploadType =
      (searchParams.get("type") as UploadConfigType) || "job-image";

    // Validate upload type
    if (!uploadConfigs[uploadType]) {
      return NextResponse.json(
        { error: "Invalid upload type" },
        { status: 400 },
      );
    }

    const config = uploadConfigs[uploadType];

    return NextResponse.json({
      uploadType,
      maxFileSize: config.maxFileSize,
      allowedTypes: config.allowedTypes,
      expiresIn: config.expiresIn,
    });
  } catch (error) {
    console.error("Error retrieving upload config:", error);

    return NextResponse.json(
      { error: "Failed to retrieve upload configuration" },
      { status: 500 },
    );
  }
}
