import Footer from "@/components/marketing/footer";
import { Meet<PERSON><PERSON><PERSON><PERSON> } from "@/components/marketing/meet-jack/hero";
import { MeetJackFeatures } from "@/components/marketing/meet-jack/features";
import { MeetJackDemo } from "@/components/marketing/meet-jack/demo";

export default function MeetJackPage() {
  // JSON-LD structured data for better SEO
  const jsonLd = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    name: "Meet Jack - Your AI Home Project Assistant",
    description:
      "Meet <PERSON>, your personal AI assistant for home improvement projects. Get instant help with project planning, contractor selection, and more.",
    mainEntity: {
      "@type": "Thing",
      name: "Jack AI Assistant",
      description: "An AI-powered assistant that helps homeowners and contractors manage home improvement projects more efficiently.",
    },
  };

  return (
    <main>
      {/* Add JSON-LD structured data */}
      <script
        type="application/ld+json"
        // biome-ignore lint/security/noDangerouslySetInnerHtml: JSON-LD structured data
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      <MeetJackHero />
      <MeetJackFeatures />
      <MeetJackDemo />
      <Footer />
    </main>
  );
}