"use client";

import {
  ArrowLeftIcon,
  BrainIcon,
  ClipboardListIcon,
  DrillIcon,
  LayoutDashboardIcon,
  ListIcon,
  NotepadTextDashedIcon,
  User2Icon,
} from "lucide-react";
import { AppSidebar } from "@/components/core/app-sidebar";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";

const menu = [
  {
    title: "Dashboard",
    href: "/admin/dashboard",
    icon: LayoutDashboardIcon,
    visible: true,
  },
  {
    title: "Jack",
    href: "/admin/jack",
    icon: BrainIcon,
    visible: true,
  },
  {
    title: "Trades",
    href: "/admin/trades",
    icon: DrillIcon,
    visible: true,
  },
  {
    title: "Projects",
    href: "/admin/projects",
    icon: ClipboardListIcon,
    visible: true,
  },
  {
    title: "Templates",
    href: "/admin/templates",
    icon: NotepadTextDashedIcon,
    visible: true,
  },
  {
    title: "Users",
    href: "/admin/users",
    icon: User2Icon,
    visible: true,
  },
  {
    title: "Waitlist",
    href: "/admin/waitlist",
    icon: ListIcon,
    visible: true,
  },
  {
    title: "User Dashboard",
    href: "/dashboard",
    icon: ArrowLeftIcon,
    visible: true,
  },
];

export default function AdminLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  return (
    <SidebarProvider>
      <AppSidebar menu={menu} />
      <SidebarInset>{children}</SidebarInset>
    </SidebarProvider>
  );
}
