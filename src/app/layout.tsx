import type { Metada<PERSON> } from "next";
import { <PERSON>eist } from "next/font/google";
import { TRPCReactProvider } from "@/components/integrations/trpc/client";
import {
  PostHogProvider,
  ServiceWorkerInit,
  ThemeProvider,
  WaitlistProvider,
} from "@/components/providers";
import { CookieConsentBanner } from "@/components/shared";
import { Toaster } from "@/components/ui/sonner";
import { env } from "@/env";

import "@/styles/globals.css";

export const metadata: Metadata = {
  metadataBase: new URL(env.APP_URL),
  title: "TradeCrews",
  description: "Connecting homeowners with the trades",
  appleWebApp: { title: "TradeCrews" },
  manifest: "/manifest.json",
};

const geist = Geist({
  subsets: ["latin"],
  variable: "--font-geist-sans",
});

export default function RootLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  return (
    <html
      lang="en"
      className={`${geist.variable} h-full`}
      suppressHydrationWarning
    >
      <head />
      <body className="h-full">
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <PostHogProvider>
            <TRPCReactProvider>
              <WaitlistProvider>
                <ServiceWorkerInit />
                {children}
                <Toaster />
                <CookieConsentBanner />
              </WaitlistProvider>
            </TRPCReactProvider>
          </PostHogProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
