"use client";

import { Check, Info, Settings } from "lucide-react";
import { useEffect, useState } from "react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Switch } from "@/components/ui/switch";
import {
  type CookiePreferences,
  useCookieConsent,
} from "@/hooks/use-cookie-consent";

type CookieCategory = {
  id: string;
  name: string;
  description: string;
  required: boolean;
};

// CookiePreferences type is now imported from use-cookie-consent.ts

const COOKIE_CATEGORIES: CookieCategory[] = [
  {
    id: "necessary",
    name: "Necessary Cookies",
    description:
      "These cookies are essential for the website to function properly and cannot be disabled.",
    required: true,
  },
  {
    id: "functional",
    name: "Functional Cookies",
    description:
      "These cookies enable personalized features and functionality.",
    required: false,
  },
  {
    id: "analytics",
    name: "Analytics Cookies",
    description:
      "These cookies help us understand how visitors interact with our website.",
    required: false,
  },
  {
    id: "marketing",
    name: "Marketing Cookies",
    description:
      "These cookies are used to track visitors across websites to display relevant advertisements.",
    required: false,
  },
];

const COOKIE_CONSENT_KEY = "cookie-consent-preferences";

export function CookieConsentBanner() {
  const [showBanner, setShowBanner] = useState(false);
  const [showPreferences, setShowPreferences] = useState(false);
  const { preferences, savePreferences, loaded } = useCookieConsent();
  const [tempPreferences, setTempPreferences] = useState<CookiePreferences>({
    ...preferences,
  });

  // Update tempPreferences when preferences change
  useEffect(() => {
    setTempPreferences({ ...preferences });
  }, [preferences]);

  useEffect(() => {
    // Check if user has already made a choice
    if (loaded) {
      // Check if there are saved preferences in localStorage
      const savedPreferences = localStorage.getItem(COOKIE_CONSENT_KEY);

      if (!savedPreferences) {
        // If no saved preferences, show the banner after a short delay
        const timer = setTimeout(() => {
          setShowBanner(true);
        }, 1000);

        return () => clearTimeout(timer);
      }
    }
  }, [loaded]);

  const handleAcceptAll = () => {
    savePreferences({
      necessary: true,
      functional: true,
      analytics: true,
      marketing: true,
    });
    setShowBanner(false);
  };

  const handleRejectNonEssential = () => {
    savePreferences({
      necessary: true,
      functional: false,
      analytics: false,
      marketing: false,
    });
    setShowBanner(false);
  };

  const handleSavePreferences = () => {
    savePreferences(tempPreferences);
    setShowPreferences(false);
    setShowBanner(false);
  };

  const handlePreferenceChange = (categoryId: string, enabled: boolean) => {
    setTempPreferences((prev: CookiePreferences) => ({
      ...prev,
      [categoryId]: enabled,
    }));
  };

  if (!showBanner && !showPreferences) {
    return null;
  }

  if (showPreferences) {
    return (
      <Dialog open={showPreferences} onOpenChange={setShowPreferences}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Cookie Preferences</DialogTitle>
            <DialogDescription>
              Manage your cookie preferences. Necessary cookies cannot be
              disabled as they are essential for the website to function.
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            <Accordion type="single" collapsible className="w-full">
              {COOKIE_CATEGORIES.map((category) => (
                <AccordionItem key={category.id} value={category.id}>
                  <div className="flex items-center justify-between py-2">
                    <div className="flex-1">
                      <AccordionTrigger className="hover:no-underline">
                        {category.name}
                        {category.required && (
                          <span className="ml-2 text-muted-foreground text-xs">
                            (Required)
                          </span>
                        )}
                      </AccordionTrigger>
                    </div>
                    <Switch
                      checked={
                        tempPreferences[category.id as keyof CookiePreferences]
                      }
                      disabled={category.required}
                      onCheckedChange={(checked) =>
                        handlePreferenceChange(category.id, checked)
                      }
                      className="mr-4"
                    />
                  </div>
                  <AccordionContent>
                    <p className="text-muted-foreground text-sm">
                      {category.description}
                    </p>
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowPreferences(false)}>
              Cancel
            </Button>
            <Button onClick={handleSavePreferences}>Save Preferences</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <div className="fixed right-0 bottom-0 left-0 z-50 border-t bg-background p-4 shadow-lg md:p-6">
      <div className="container mx-auto max-w-7xl">
        <div className="flex flex-col items-start justify-between gap-4 md:flex-row md:items-center">
          <div className="flex items-start gap-3">
            <Info className="mt-0.5 h-5 w-5 flex-shrink-0" />
            <div>
              <h3 className="mb-1 font-medium">We use cookies</h3>
              <p className="text-muted-foreground text-sm">
                We use cookies to enhance your browsing experience, serve
                personalized ads or content, and analyze our traffic. By
                clicking "Accept All", you consent to our use of cookies.
              </p>
            </div>
          </div>

          <div className="mt-2 flex flex-wrap gap-2 md:mt-0">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowPreferences(true)}
              className="flex items-center gap-1"
            >
              <Settings className="h-4 w-4" />
              <span>Preferences</span>
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleRejectNonEssential}
            >
              Reject Non-Essential
            </Button>
            <Button
              size="sm"
              onClick={handleAcceptAll}
              className="flex items-center gap-1"
            >
              <Check className="h-4 w-4" />
              <span>Accept All</span>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

// Component to manage cookie preferences from other parts of the app
export function CookiePreferencesButton({ className }: { className?: string }) {
  const [showPreferences, setShowPreferences] = useState(false);
  const { preferences, savePreferences } = useCookieConsent();

  // Listen for custom event to show preferences
  useEffect(() => {
    const handleShowPreferences = () => {
      setShowPreferences(true);
    };

    window.addEventListener("show-cookie-preferences", handleShowPreferences);

    return () => {
      window.removeEventListener(
        "show-cookie-preferences",
        handleShowPreferences,
      );
    };
  }, []);

  const [tempPreferences, setTempPreferences] = useState<CookiePreferences>({
    ...preferences,
  });

  // Update tempPreferences when preferences change
  useEffect(() => {
    setTempPreferences({ ...preferences });
  }, [preferences]);

  const handlePreferenceChange = (categoryId: string, enabled: boolean) => {
    setTempPreferences((prev) => ({
      ...prev,
      [categoryId]: enabled,
    }));
  };

  const handleSavePreferences = () => {
    // Save the temporary preferences
    savePreferences(tempPreferences);
    setShowPreferences(false);
  };

  return (
    <>
      <Button
        variant="outline"
        size="sm"
        onClick={() => setShowPreferences(true)}
        className={className}
      >
        Cookie Preferences
      </Button>

      {showPreferences && (
        <Dialog open={showPreferences} onOpenChange={setShowPreferences}>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>Cookie Preferences</DialogTitle>
              <DialogDescription>
                Manage your cookie preferences. Necessary cookies cannot be
                disabled as they are essential for the website to function.
              </DialogDescription>
            </DialogHeader>

            <div className="py-4">
              <Accordion type="single" collapsible className="w-full">
                {COOKIE_CATEGORIES.map((category) => (
                  <AccordionItem key={category.id} value={category.id}>
                    <div className="flex items-center justify-between py-2">
                      <div className="flex-1">
                        <AccordionTrigger className="hover:no-underline">
                          {category.name}
                          {category.required && (
                            <span className="ml-2 text-muted-foreground text-xs">
                              (Required)
                            </span>
                          )}
                        </AccordionTrigger>
                      </div>
                      <Switch
                        checked={
                          tempPreferences[
                            category.id as keyof CookiePreferences
                          ]
                        }
                        disabled={category.required}
                        onCheckedChange={(checked) =>
                          handlePreferenceChange(category.id, checked)
                        }
                        className="mr-4"
                      />
                    </div>
                    <AccordionContent>
                      <p className="text-muted-foreground text-sm">
                        {category.description}
                      </p>
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            </div>

            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setShowPreferences(false)}
              >
                Cancel
              </Button>
              <Button onClick={handleSavePreferences}>Save Preferences</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </>
  );
}
