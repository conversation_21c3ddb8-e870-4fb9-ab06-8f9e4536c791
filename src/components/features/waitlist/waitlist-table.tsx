"use client";

import { useQuery } from "@tanstack/react-query";
import { useState } from "react";
import { DataTable } from "@/components/core/data-table";
import { useTRPC } from "@/components/integrations/trpc/client";
import { Card, CardContent } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { waitlistColumns } from "./columns";

interface WaitlistTableProps {
  initialStatus?: "PENDING" | "APPROVED" | "REJECTED" | "INVITED";
  initialAccountType?: "homeowner" | "contractor";
}

export function WaitlistTable({
  initialStatus,
  initialAccountType,
}: WaitlistTableProps) {
  const trpc = useTRPC();

  const [status, setStatus] = useState<
    "PENDING" | "APPROVED" | "REJECTED" | "INVITED" | undefined
  >(initialStatus);
  const [accountType, setAccountType] = useState<
    "homeowner" | "contractor" | undefined
  >(initialAccountType);

  const { data, isLoading, error } = useQuery(
    trpc.waitlist.list.queryOptions({
      status,
      accountType,
      limit: 100,
    }),
  );

  if (isLoading) {
    return <WaitlistTableSkeleton />;
  }

  if (error) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center p-6 text-red-500">
          <p>Error loading waitlist data: {error.message}</p>
        </CardContent>
      </Card>
    );
  }

  if (!data || data.entries.length === 0) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center p-6">
          <p className="text-muted-foreground">No waitlist entries found</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex flex-wrap gap-4">
        <div className="w-full md:w-48">
          <Select
            value={status || ""}
            onValueChange={(value) =>
              setStatus(
                value
                  ? (value as "PENDING" | "APPROVED" | "REJECTED" | "INVITED")
                  : undefined,
              )
            }
          >
            <SelectTrigger>
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All Statuses</SelectItem>
              <SelectItem value="PENDING">Pending</SelectItem>
              <SelectItem value="APPROVED">Approved</SelectItem>
              <SelectItem value="REJECTED">Rejected</SelectItem>
              <SelectItem value="INVITED">Invited</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="w-full md:w-48">
          <Select
            value={accountType || ""}
            onValueChange={(value) =>
              setAccountType(
                value ? (value as "homeowner" | "contractor") : undefined,
              )
            }
          >
            <SelectTrigger>
              <SelectValue placeholder="Filter by type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All Types</SelectItem>
              <SelectItem value="homeowner">Homeowner</SelectItem>
              <SelectItem value="contractor">Contractor</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <Card>
        <CardContent className="p-0">
          <DataTable columns={waitlistColumns} data={data.entries} />
        </CardContent>
      </Card>
    </div>
  );
}

function WaitlistTableSkeleton() {
  return (
    <Card>
      <CardContent className="p-6">
        <div className="space-y-4">
          <div className="flex gap-4">
            <Skeleton className="h-10 w-48" />
            <Skeleton className="h-10 w-48" />
          </div>
          <Skeleton className="h-[400px] w-full" />
        </div>
      </CardContent>
    </Card>
  );
}
