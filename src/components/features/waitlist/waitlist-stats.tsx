"use client";

import { useQuery } from "@tanstack/react-query";
import { ListIcon, MailIcon, UserCheckIcon, UserIcon } from "lucide-react";
import { useTRPC } from "@/components/integrations/trpc/client";
import { Card, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

export function WaitlistStats() {
  const trpc = useTRPC();

  const {
    data: waitlistData,
    isLoading,
    error,
  } = useQuery(
    trpc.waitlist.list.queryOptions({
      limit: 1000, // Get all entries for stats
    }),
  );

  if (isLoading) {
    return <WaitlistStatsSkeleton />;
  }

  if (error) {
    return (
      <div className="p-4 text-red-500">
        Error loading waitlist data: {error.message}
      </div>
    );
  }

  if (!waitlistData) {
    return null;
  }

  const entries = waitlistData.entries;
  const total = entries.length;

  // Count by status
  const pending = entries.filter((entry) => entry.status === "PENDING").length;
  const approved = entries.filter(
    (entry) => entry.status === "APPROVED",
  ).length;
  const invited = entries.filter((entry) => entry.status === "INVITED").length;

  // Count by account type
  const homeowners = entries.filter(
    (entry) => entry.accountType === "homeowner",
  ).length;
  const contractors = entries.filter(
    (entry) => entry.accountType === "contractor",
  ).length;

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card>
        <CardContent className="flex items-center p-6">
          <ListIcon className="h-8 w-8 text-blue-600" />
          <div className="ml-4">
            <p className="font-medium text-muted-foreground text-sm">
              Total Entries
            </p>
            <p className="font-bold text-2xl">{total}</p>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="flex items-center p-6">
          <UserCheckIcon className="h-8 w-8 text-green-600" />
          <div className="ml-4">
            <p className="font-medium text-muted-foreground text-sm">
              Approved
            </p>
            <p className="font-bold text-2xl">{approved}</p>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="flex items-center p-6">
          <MailIcon className="h-8 w-8 text-purple-600" />
          <div className="ml-4">
            <p className="font-medium text-muted-foreground text-sm">Invited</p>
            <p className="font-bold text-2xl">{invited}</p>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="flex items-center p-6">
          <UserIcon className="h-8 w-8 text-orange-600" />
          <div className="ml-4">
            <p className="font-medium text-muted-foreground text-sm">Pending</p>
            <p className="font-bold text-2xl">{pending}</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export function WaitlistStatsSkeleton() {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {Array(4)
        .fill(0)
        .map((_, i) => (
          // biome-ignore lint/suspicious/noArrayIndexKey: Static skeleton items
          <Card key={i}>
            <CardContent className="flex items-center p-6">
              <Skeleton className="h-8 w-8 rounded" />
              <div className="ml-4 space-y-2">
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-6 w-12" />
              </div>
            </CardContent>
          </Card>
        ))}
    </div>
  );
}
