"use client";

import { useMutation, useQueryClient } from "@tanstack/react-query";
import {
  CheckI<PERSON>,
  MailIcon,
  MoreHorizontalIcon,
  TrashIcon,
  XIcon,
} from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import { useTRPC } from "@/components/integrations/trpc/client";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import type { Waitlist } from "@/db/schema";

interface WaitlistActionsProps {
  entry: Waitlist;
}

export function WaitlistActions({ entry }: WaitlistActionsProps) {
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const trpc = useTRPC();
  const queryClient = useQueryClient();

  const updateStatus = useMutation(
    trpc.waitlist.updateStatus.mutationOptions({
      onSuccess: () => {
        queryClient.invalidateQueries({
          queryKey: trpc.waitlist.list.queryKey(),
        });
        toast.success("Status updated successfully");
      },
      onError: (error) => {
        const errorMessage = error.message || "Unknown error occurred";
        toast.error(`Failed to update status: ${errorMessage}`);
      },
    }),
  );

  const deleteEntry = useMutation(
    trpc.waitlist.delete.mutationOptions({
      onSuccess: () => {
        queryClient.invalidateQueries({
          queryKey: trpc.waitlist.list.queryKey(),
        });
        toast.success("Waitlist entry deleted successfully");
      },
      onError: (error) => {
        const errorMessage = error.message || "Unknown error occurred";
        toast.error(`Failed to delete entry: ${errorMessage}`);
      },
    }),
  );

  const handleStatusUpdate = (
    status: "PENDING" | "APPROVED" | "REJECTED" | "INVITED",
  ) => {
    updateStatus.mutate({
      id: entry.id,
      status,
    });
  };

  const handleDelete = () => {
    deleteEntry.mutate(entry.id);
    setIsDeleteDialogOpen(false);
  };

  const isLoading = updateStatus.isPending || deleteEntry.isPending;

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="icon" disabled={isLoading}>
            <MoreHorizontalIcon className="h-4 w-4" />
            <span className="sr-only">Open menu</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          {entry.status !== "APPROVED" && (
            <DropdownMenuItem
              onClick={() => handleStatusUpdate("APPROVED")}
              disabled={isLoading}
            >
              <CheckIcon className="mr-2 h-4 w-4 text-green-500" />
              Approve
            </DropdownMenuItem>
          )}

          {entry.status !== "REJECTED" && (
            <DropdownMenuItem
              onClick={() => handleStatusUpdate("REJECTED")}
              disabled={isLoading}
            >
              <XIcon className="mr-2 h-4 w-4 text-red-500" />
              Reject
            </DropdownMenuItem>
          )}

          {entry.status !== "INVITED" && entry.status === "APPROVED" && (
            <DropdownMenuItem
              onClick={() => handleStatusUpdate("INVITED")}
              disabled={isLoading}
            >
              <MailIcon className="mr-2 h-4 w-4 text-blue-500" />
              Send Invite
            </DropdownMenuItem>
          )}

          <DropdownMenuItem
            onClick={() => setIsDeleteDialogOpen(true)}
            disabled={isLoading}
            className="text-red-600"
          >
            <TrashIcon className="mr-2 h-4 w-4" />
            Delete
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <AlertDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the waitlist entry for {entry.name} (
              {entry.email}). This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isLoading}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              disabled={isLoading}
              className="bg-red-600 hover:bg-red-700"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
