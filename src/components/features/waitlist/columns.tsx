import type { ColumnDef } from "@tanstack/react-table";
import { format } from "date-fns";
import { Badge } from "@/components/ui/badge";
import type { Waitlist } from "@/db/schema";
import { WaitlistActions } from "./waitlist-actions";

export const waitlistColumns: ColumnDef<Waitlist>[] = [
  {
    accessorKey: "name",
    header: "Name",
  },
  {
    accessorKey: "email",
    header: "Email",
  },
  {
    accessorKey: "accountType",
    header: "Account Type",
    cell: ({ row }) => {
      const accountType = row.getValue("accountType") as string;
      return (
        <Badge variant={accountType === "contractor" ? "outline" : "secondary"}>
          {accountType.charAt(0).toUpperCase() + accountType.slice(1)}
        </Badge>
      );
    },
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => {
      const status = row.getValue("status") as string;

      const getVariant = () => {
        switch (status) {
          case "APPROVED":
            return "success";
          case "REJECTED":
            return "destructive";
          case "INVITED":
            return "default";
          default:
            return "outline";
        }
      };

      return <Badge variant={getVariant()}>{status}</Badge>;
    },
  },
  {
    accessorKey: "createdAt",
    header: "Date Applied",
    cell: ({ row }) => {
      const date = row.getValue("createdAt") as Date;
      return format(new Date(date), "MMM d, yyyy");
    },
  },
  {
    accessorKey: "invitedAt",
    header: "Date Invited",
    cell: ({ row }) => {
      const date = row.getValue("invitedAt") as Date | null;
      return date ? format(new Date(date), "MMM d, yyyy") : "-";
    },
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const waitlistEntry = row.original;
      return <WaitlistActions entry={waitlistEntry} />;
    },
  },
];
