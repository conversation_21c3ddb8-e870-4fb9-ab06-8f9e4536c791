"use client";

import { type Driver, driver } from "driver.js";
import { useEffect, useRef } from "react";
import "driver.js/dist/driver.css";
import { useLocalStorage } from "@/hooks/use-local-storage";

interface PropertyWizardTourProps {
  isOpen: boolean;
  currentStep: number;
}

// Define tour steps for each wizard step outside component to prevent recreation
const TOUR_STEPS = {
  1: [
    // Basics step
    {
      element: "[name='name']",
      popover: {
        title: "Property Name",
        description:
          "Give your property a memorable name. This could be the address, a nickname, or any identifier that helps you recognize it.",
        side: "bottom" as const,
      },
    },
    {
      element: "[name='type']",
      popover: {
        title: "Property Type",
        description:
          "Select the type of property. This helps contractors understand the context and scope of potential projects.",
        side: "top" as const,
      },
    },
    {
      element: ".grid.grid-cols-2.gap-4",
      popover: {
        title: "Property Types",
        description:
          "Choose from Residential, Commercial, Rental, or Vacation property types. Each type has different considerations for contractors.",
        side: "left" as const,
      },
    },
    {
      element: "[name='description']",
      popover: {
        title: "Property Description",
        description:
          "Add any additional details about your property that might be helpful for contractors to know.",
        side: "top" as const,
      },
    },
  ],
  2: [
    // Location step
    {
      element: "[name='addressStreet']",
      popover: {
        title: "Street Address",
        description:
          "Enter the complete street address of your property. This helps contractors calculate travel time and costs.",
        side: "bottom" as const,
      },
    },
    {
      element: "[name='addressCity']",
      popover: {
        title: "City",
        description: "The city where your property is located.",
        side: "bottom" as const,
      },
    },
    {
      element: "[name='addressState']",
      popover: {
        title: "State Selection",
        description:
          "Choose your state from the dropdown. This helps match you with local contractors in your area.",
        side: "top" as const,
      },
    },
    {
      element: "[name='addressZip']",
      popover: {
        title: "ZIP Code",
        description:
          "Enter the ZIP code for precise location matching with nearby contractors.",
        side: "top" as const,
      },
    },
  ],
  3: [
    // Images step
    {
      element: ".uppy-Dashboard",
      popover: {
        title: "Property Photos",
        description:
          "Add photos of your property to help contractors understand the space and context for future projects.",
        side: "top" as const,
      },
    },
    {
      element: ".uppy-Dashboard-AddFiles",
      popover: {
        title: "Upload Images",
        description:
          "Click here to upload photos, or simply drag and drop image files into this area.",
        side: "bottom" as const,
      },
    },
  ],
  4: [
    // Review step
    {
      element: ".space-y-6",
      popover: {
        title: "Review Property Details",
        description:
          "Review all the information you've entered to make sure everything is correct before creating your property.",
        side: "top" as const,
      },
    },
    {
      element: "button:contains('Create Property')",
      popover: {
        title: "Create Your Property",
        description:
          "Once everything looks good, click here to add this property to your account. You can then create projects for this property!",
        side: "top" as const,
      },
    },
  ],
};

export function PropertyWizardTour({
  isOpen,
  currentStep,
}: PropertyWizardTourProps) {
  const [hasSeenTour, setHasSeenTour] = useLocalStorage(
    "seen-property-wizard-tour",
    false,
  );
  const driverInstanceRef = useRef<Driver | null>(null);

  useEffect(() => {
    if (!isOpen || hasSeenTour || !currentStep) return;

    // Clean up previous instance
    if (driverInstanceRef.current) {
      driverInstanceRef.current.destroy();
      driverInstanceRef.current = null;
    }

    const steps = TOUR_STEPS[currentStep as keyof typeof TOUR_STEPS];
    if (!steps || steps.length === 0) return;

    const instance = driver({
      showProgress: true,
      steps,
      onDestroyStarted: () => {
        if (currentStep === 4) {
          // Last step - use a timeout to avoid state update during render
          setTimeout(() => setHasSeenTour(true), 0);
        }
      },
      onDestroyed: () => {
        if (currentStep === 4) {
          // Last step - use a timeout to avoid state update during render
          setTimeout(() => setHasSeenTour(true), 0);
        }
      },
    });

    driverInstanceRef.current = instance;

    // Start the tour after a short delay to ensure all elements are loaded
    const timer = setTimeout(() => {
      instance.drive();
    }, 500);

    return () => {
      clearTimeout(timer);
      if (driverInstanceRef.current) {
        driverInstanceRef.current.destroy();
        driverInstanceRef.current = null;
      }
    };
  }, [isOpen, currentStep, hasSeenTour, setHasSeenTour]);

  return null;
}
