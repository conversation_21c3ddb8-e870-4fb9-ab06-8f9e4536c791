"use client";

import { CloudflareSingleImageUploader } from "../upload/cloudflare-single-image-uploader";

interface PropertyImageUploaderProps {
  initialImageUrl?: string;
  onImageUploaded: (imageUrl: string) => void;
  onImageRemoved: () => void;
}

export function PropertyImageUploader({
  initialImageUrl,
  onImageUploaded,
  onImageRemoved,
}: PropertyImageUploaderProps) {
  return (
    <CloudflareSingleImageUploader
      value={initialImageUrl}
      onChange={(url) => {
        if (url) {
          onImageUploaded(url);
        } else {
          onImageRemoved();
        }
      }}
      uploadType="property-image"
      size="lg"
    />
  );
}
