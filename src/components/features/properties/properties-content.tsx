"use client";

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  Building2,
  Grid3X3,
  List,
  MapPin,
  Plus,
  RefreshCw,
  Search,
  SlidersHorizontal,
  Trash2,
} from "lucide-react";
import Image from "next/image";
import { useMemo, useState } from "react";
import { toast } from "sonner";
import { NewProperty } from "@/components/features/properties/new-property";
import { PropertyCard } from "@/components/features/properties/property-card";
import { useTRPC } from "@/components/integrations/trpc/client";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { CardGrid } from "@/components/ui/responsive-grid";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { formatDate } from "@/lib/utils";

type SortOption = "name" | "created" | "address" | "recent-activity";
type ViewMode = "grid" | "list";

export function PropertiesContent() {
  const trpc = useTRPC();
  const queryClient = useQueryClient();
  const [searchQuery, setSearchQuery] = useState("");
  const [sortBy, setSortBy] = useState<SortOption>("name");
  const [viewMode, setViewMode] = useState<ViewMode>("grid");
  const [showFilters, setShowFilters] = useState(false);
  const [showDeleted, setShowDeleted] = useState(false);

  // Query for active properties
  const { data: properties, isLoading } = useQuery(
    trpc.properties.list.queryOptions(),
  );

  // Query for deleted properties
  const { data: deletedProperties } = useQuery(
    trpc.properties.listDeleted.queryOptions(undefined, {
      enabled: showDeleted,
    }),
  );

  const restoreMutation = useMutation(
    trpc.properties.restore.mutationOptions({
      onSuccess: () => {
        queryClient.invalidateQueries({
          queryKey: trpc.properties.list.queryKey(),
        });
        queryClient.invalidateQueries({
          queryKey: trpc.properties.listDeleted.queryKey(),
        });
        toast.success("Property restored successfully");
      },
      onError: (error) => {
        toast.error(`Error restoring property: ${error.message}`);
      },
    }),
  );

  // Function to restore a deleted property
  const restoreProperty = async (propertyId: string) => {
    await restoreMutation.mutateAsync({ id: propertyId });
  };

  // Enhanced filtering and sorting
  const filteredAndSortedProperties = useMemo(() => {
    // If showing deleted properties, use that list instead
    if (showDeleted) {
      if (!deletedProperties) return [];

      const filtered = deletedProperties.filter((property) => {
        const searchLower = searchQuery.toLowerCase();
        const nameMatch = property.name.toLowerCase().includes(searchLower);
        const addressMatch = property.address
          ? `${property.address.street} ${property.address.city} ${property.address.state}`
              .toLowerCase()
              .includes(searchLower)
          : false;
        return nameMatch || addressMatch;
      });

      return filtered;
    }

    // Otherwise show active properties
    if (!properties) return [];

    const filtered = properties.filter((property) => {
      const searchLower = searchQuery.toLowerCase();
      const nameMatch = property.name.toLowerCase().includes(searchLower);
      const addressMatch = property.addressStreet
        ? `${property.addressStreet} ${property.addressCity} ${property.addressState}`
            .toLowerCase()
            .includes(searchLower)
        : false;

      return nameMatch || addressMatch;
    });

    // Sort properties
    filtered.sort((a, b) => {
      switch (sortBy) {
        case "name":
          return a.name.localeCompare(b.name);
        case "created":
          return (
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
          );
        case "address": {
          const aAddress = a.addressCity
            ? `${a.addressCity}, ${a.addressState}`
            : "";
          const bAddress = b.addressCity
            ? `${b.addressCity}, ${b.addressState}`
            : "";
          return aAddress.localeCompare(bAddress);
        }
        case "recent-activity":
          // This would require additional data about recent jobs/activity
          return (
            new Date(b.updatedAt || b.createdAt).getTime() -
            new Date(a.updatedAt || a.createdAt).getTime()
          );
        default:
          return 0;
      }
    });

    return filtered;
  }, [properties, deletedProperties, searchQuery, sortBy, showDeleted]);

  // Get statistics
  const stats = useMemo(() => {
    if (showDeleted) {
      if (!deletedProperties)
        return { total: 0, cities: 0, states: 0, deleted: 0 };

      const cities = new Set(
        deletedProperties.map((p) => p.address?.city).filter(Boolean),
      );
      const states = new Set(
        deletedProperties.map((p) => p.address?.state).filter(Boolean),
      );

      return {
        total: deletedProperties.length,
        cities: cities.size,
        states: states.size,
        deleted: deletedProperties.length,
      };
    }

    if (!properties) return { total: 0, cities: 0, states: 0, deleted: 0 };

    const cities = new Set(
      properties.map((p) => p.addressCity).filter(Boolean),
    );
    const states = new Set(
      properties.map((p) => p.addressState).filter(Boolean),
    );

    return {
      total: properties.length,
      cities: cities.size,
      states: states.size,
      deleted: deletedProperties?.length || 0,
    };
  }, [properties, deletedProperties, showDeleted]);

  if (isLoading) {
    return (
      <div className="space-y-6">
        {/* Header Skeleton */}
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div className="space-y-2">
            <div className="h-8 w-48 animate-pulse rounded bg-muted" />
            <div className="h-4 w-64 animate-pulse rounded bg-muted" />
          </div>
          <div className="h-10 w-32 animate-pulse rounded bg-muted" />
        </div>

        {/* Stats Skeleton */}
        <div className="grid gap-4 sm:grid-cols-3">
          {Array.from({ length: 3 }).map((_, i) => (
            // biome-ignore lint/suspicious/noArrayIndexKey: Static skeleton items
            <div key={i} className="h-20 animate-pulse rounded-lg bg-muted" />
          ))}
        </div>

        {/* Search/Filter Skeleton */}
        <div className="flex flex-col gap-4 sm:flex-row">
          <div className="h-10 flex-1 animate-pulse rounded bg-muted" />
          <div className="h-10 w-32 animate-pulse rounded bg-muted" />
        </div>

        {/* Grid Skeleton */}
        <CardGrid>
          {Array.from({ length: 6 }).map((_, i) => (
            // biome-ignore lint/suspicious/noArrayIndexKey: Static skeleton items
            <div key={i} className="h-80 animate-pulse rounded-lg bg-muted" />
          ))}
        </CardGrid>
      </div>
    );
  }

  // Show empty state for active properties
  if (!showDeleted && (!properties || properties.length === 0)) {
    return (
      <div className="space-y-6">
        {/* Empty State Header */}
        <div className="py-12 text-center">
          <div className="mx-auto mb-4 flex h-24 w-24 items-center justify-center rounded-full bg-muted">
            <MapPin className="h-12 w-12 text-muted-foreground" />
          </div>
          <h3 className="mb-2 font-semibold text-lg">No Properties Yet</h3>
          <p className="mx-auto mb-6 max-w-md text-muted-foreground">
            Get started by adding your first property. You can then create
            projects and manage work for each location.
          </p>
        </div>

        <CardGrid>
          <NewProperty />
        </CardGrid>
      </div>
    );
  }

  // Show empty state for deleted properties
  if (showDeleted && (!deletedProperties || deletedProperties.length === 0)) {
    return (
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="font-bold text-2xl tracking-tight">
              Deleted Properties
            </h1>
            <p className="text-muted-foreground">
              View and restore previously deleted properties
            </p>
          </div>
          <Button variant="outline" onClick={() => setShowDeleted(false)}>
            <RefreshCw className="mr-2 h-4 w-4" />
            Show Active Properties
          </Button>
        </div>

        {/* Empty State */}
        <div className="py-12 text-center">
          <div className="mx-auto mb-4 flex h-24 w-24 items-center justify-center rounded-full bg-muted">
            <Trash2 className="h-12 w-12 text-muted-foreground" />
          </div>
          <h3 className="mb-2 font-semibold text-lg">No Deleted Properties</h3>
          <p className="mx-auto mb-6 max-w-md text-muted-foreground">
            You don't have any deleted properties. When you delete properties,
            they'll appear here.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          {showDeleted ? (
            <>
              <h1 className="font-bold text-2xl tracking-tight">
                Deleted Properties
              </h1>
              <p className="text-muted-foreground">
                View and restore previously deleted properties
              </p>
            </>
          ) : (
            <>
              <h1 className="font-bold text-2xl tracking-tight">Properties</h1>
              <p className="text-muted-foreground">
                Manage your properties and track project history
              </p>
            </>
          )}
        </div>
        {showDeleted ? (
          <Button variant="outline" onClick={() => setShowDeleted(false)}>
            <RefreshCw className="mr-2 h-4 w-4" />
            Show Active Properties
          </Button>
        ) : (
          <Button
            onClick={() =>
              document.getElementById("new-property-trigger")?.click()
            }
          >
            <Plus className="mr-2 h-4 w-4" />
            Add Property
          </Button>
        )}
      </div>

      {/* Statistics Cards */}
      <div className="grid gap-4 sm:grid-cols-3">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="rounded-lg bg-tradecrews-orange/10 p-2">
                <MapPin className="h-5 w-5 text-tradecrews-orange-600" />
              </div>
              <div>
                <p className="font-bold text-2xl">{stats.total}</p>
                <p className="text-muted-foreground text-sm">
                  Total Properties
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="rounded-lg bg-blue-100 p-2">
                <MapPin className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="font-bold text-2xl">{stats.cities}</p>
                <p className="text-muted-foreground text-sm">Cities</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="rounded-lg bg-green-100 p-2">
                <MapPin className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="font-bold text-2xl">{stats.states}</p>
                <p className="text-muted-foreground text-sm">States</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
        <div className="relative flex-1">
          <Search className="-translate-y-1/2 absolute top-1/2 left-3 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search properties by name or address..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-9"
          />
        </div>

        <div className="flex items-center gap-2">
          <Select
            value={sortBy}
            onValueChange={(value: SortOption) => setSortBy(value)}
          >
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Sort by..." />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="name">Name (A-Z)</SelectItem>
              <SelectItem value="created">Recently Added</SelectItem>
              <SelectItem value="address">Location</SelectItem>
              <SelectItem value="recent-activity">Recent Activity</SelectItem>
            </SelectContent>
          </Select>

          {/* View Mode Switcher */}
          <div className="flex items-center rounded-md border">
            <Button
              variant={viewMode === "grid" ? "default" : "ghost"}
              size="sm"
              onClick={() => setViewMode("grid")}
              className="rounded-r-none border-r"
            >
              <Grid3X3 className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === "list" ? "default" : "ghost"}
              size="sm"
              onClick={() => setViewMode("list")}
              className="rounded-l-none"
            >
              <List className="h-4 w-4" />
            </Button>
          </div>

          <div className="flex gap-2">
            <Button
              variant={showDeleted ? "default" : "outline"}
              size="sm"
              className={showDeleted ? "bg-red-500 hover:bg-red-600" : ""}
              onClick={() => setShowDeleted(!showDeleted)}
            >
              {showDeleted ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Deleted ({stats.deleted})
                </>
              ) : (
                <>
                  <Trash2 className="mr-2 h-4 w-4" />
                  Show Deleted
                </>
              )}
            </Button>

            <Button
              variant="outline"
              size="icon"
              onClick={() => setShowFilters(!showFilters)}
            >
              <SlidersHorizontal className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Results Summary */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <span className="text-muted-foreground text-sm">
            Showing {filteredAndSortedProperties.length} of {stats.total}{" "}
            {showDeleted ? "deleted properties" : "properties"}
          </span>
          {searchQuery && (
            <Badge variant="secondary" className="text-xs">
              Search: "{searchQuery}"
            </Badge>
          )}
        </div>
      </div>

      {/* Properties Grid */}
      {filteredAndSortedProperties.length === 0 ? (
        <div className="py-12 text-center">
          <Search className="mx-auto mb-4 h-12 w-12 text-muted-foreground" />
          <h3 className="mb-2 font-semibold text-lg">No Properties Found</h3>
          <p className="mb-4 text-muted-foreground">
            Try adjusting your search terms or filters
          </p>
          <Button variant="outline" onClick={() => setSearchQuery("")}>
            Clear Search
          </Button>
        </div>
      ) : (
        <div className={viewMode === "list" ? "space-y-4" : ""}>
          <CardGrid className={viewMode === "list" ? "grid-cols-1" : ""}>
            {filteredAndSortedProperties.map((property) => (
              <div key={property.id} className="relative">
                {showDeleted ? (
                  <Card className="overflow-hidden">
                    <div className="relative">
                      {property.imageUrl ? (
                        <div className="h-48 w-full overflow-hidden bg-muted">
                          <Image
                            src={property.imageUrl}
                            alt={property.name}
                            className="h-full w-full object-cover opacity-50"
                          />
                          <div className="absolute inset-0 flex items-center justify-center bg-black/20">
                            <Badge className="bg-red-500 text-white">
                              Deleted
                            </Badge>
                          </div>
                        </div>
                      ) : (
                        <div className="flex h-48 w-full items-center justify-center bg-muted">
                          <Building2 className="h-12 w-12 text-muted-foreground opacity-50" />
                          <Badge className="absolute bg-red-500 text-white">
                            Deleted
                          </Badge>
                        </div>
                      )}
                    </div>
                    <CardContent className="p-4">
                      <h3 className="line-clamp-1 font-semibold text-lg">
                        {property.name}
                      </h3>
                      <p className="mb-2 text-muted-foreground text-sm">
                        {property.address.street}, {property.address.city},{" "}
                        {property.address.state}
                      </p>
                      {property.deletedAt && (
                        <p className="mb-4 text-muted-foreground text-xs">
                          Deleted on: {formatDate(property.deletedAt)}
                        </p>
                      )}
                      <Button
                        className="w-full bg-green-500 hover:bg-green-600"
                        onClick={() => restoreProperty(property.id)}
                      >
                        <RefreshCw className="mr-2 h-4 w-4" />
                        Restore Property
                      </Button>
                    </CardContent>
                  </Card>
                ) : (
                  <PropertyCard
                    property={property}
                    showStats={true}
                    compact={viewMode === "list"}
                  />
                )}
              </div>
            ))}
            {!showDeleted && <NewProperty />}
          </CardGrid>
        </div>
      )}
    </div>
  );
}
