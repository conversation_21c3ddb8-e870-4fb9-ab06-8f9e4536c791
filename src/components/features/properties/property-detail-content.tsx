"use client";

import { useQuery } from "@tanstack/react-query";
import { format } from "date-fns";
import {
  Building2,
  Calendar,
  Camera,
  ChevronRight,
  ClockIcon,
  Info,
  MapPin,
  Tag,
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useState } from "react";
import { useTRPC } from "@/components/integrations/trpc/client";
import { Badge } from "@/components/ui/badge";
import { Button, buttonVariants } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";
import { ProjectWizard } from "../projects/project-wizard";

export function PropertyDetailContent({ id }: { id: string }) {
  const trpc = useTRPC();
  const [projectWizardOpen, setProjectWizardOpen] = useState(false);
  const [imageViewerOpen, setImageViewerOpen] = useState(false);
  const [reportsDialogOpen, setReportsDialogOpen] = useState(false);

  const { data: property } = useQuery(
    trpc.properties.getById.queryOptions({ id }),
  );
  const { data: propertyJobs, isLoading: isLoadingJobs } = useQuery(
    trpc.projects.listByProperty.queryOptions({ propertyId: id }),
  );

  if (!property) {
    return (
      <div className="text-center">
        <p>The requested property could not be found.</p>
        <Link
          href="/properties"
          className={buttonVariants({
            variant: "default",
            className: "mt-4",
          })}
        >
          Back to Properties
        </Link>
      </div>
    );
  }

  // Group jobs by year
  const jobsByYear = propertyJobs?.reduce(
    (acc, job) => {
      if (job.completedAt) {
        const year = format(new Date(job.completedAt), "yyyy");
        if (!acc[year]) {
          acc[year] = [];
        }
        acc[year].push(job);
      }
      return acc;
    },
    {} as Record<string, typeof propertyJobs>,
  );

  // Sort years in descending order
  const sortedYears = Object.keys(jobsByYear || {}).sort(
    (a, b) => Number.parseInt(b) - Number.parseInt(a),
  );

  return (
    <>
      <div className="container mx-auto p-4 md:p-8">
        <div className="mb-8 grid grid-cols-1 gap-8 lg:grid-cols-12">
          {/* Property Image Gallery */}
          <div className="relative lg:col-span-7">
            <div className="group relative h-[400px] overflow-hidden rounded-xl">
              <Image
                src={property.imageUrl || ""}
                alt={property.name}
                fill
                className="object-cover transition-transform duration-300 group-hover:scale-105"
                priority
              />
              <div className="absolute right-4 bottom-4 flex gap-2">
                <Button
                  size="icon"
                  variant="secondary"
                  className="h-8 w-8 rounded-full bg-white/80 backdrop-blur-sm"
                  onClick={() => setImageViewerOpen(true)}
                >
                  <Camera className="h-4 w-4" />
                </Button>
              </div>
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
            </div>
          </div>

          {/* Property Details */}
          <div className="lg:col-span-5">
            <Card className="h-full">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="font-bold text-2xl">
                    {property.name}
                  </CardTitle>
                  <Button variant="outline" size="sm" asChild>
                    <Link href={`/properties/${id}/edit`}>Edit Property</Link>
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="rounded-lg bg-muted/50 p-4">
                  <div className="mb-4">
                    <h3 className="font-medium text-muted-foreground text-sm">
                      Address
                    </h3>
                    {property.address && (
                      <div className="mt-1.5 flex items-start gap-2">
                        <MapPin className="mt-1 h-5 w-5 text-orange-600" />
                        <div className="space-y-1">
                          <p className="font-medium">
                            {property.address.street}
                          </p>
                          <p className="text-muted-foreground text-sm">
                            {property.address.city}, {property.address.state}{" "}
                            {property.address.zip}
                          </p>
                        </div>
                      </div>
                    )}
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <h3 className="font-medium text-muted-foreground text-sm">
                        Property Type
                      </h3>
                      <div className="mt-1.5 flex items-center gap-2">
                        <Building2 className="h-5 w-5 text-orange-600" />
                        <Badge
                          variant="outline"
                          className="bg-orange-50 text-orange-700"
                        >
                          {property.type || "Property"}
                        </Badge>
                      </div>
                    </div>

                    <div>
                      <h3 className="font-medium text-muted-foreground text-sm">
                        Added Date
                      </h3>
                      <div className="mt-1.5 flex items-center gap-2">
                        <Calendar className="h-5 w-5 text-orange-600" />
                        <span className="text-sm">
                          {format(new Date(property.createdAt), "MMM d, yyyy")}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="mb-3 font-medium">Quick Actions</h3>
                  <div className="grid grid-cols-2 gap-3">
                    <Button
                      variant="outline"
                      className="w-full justify-start gap-2"
                      onClick={() => setProjectWizardOpen(true)}
                    >
                      <Tag className="h-4 w-4" />
                      New Project
                    </Button>
                    <Button
                      variant="outline"
                      className="w-full justify-start gap-2"
                      onClick={() => setReportsDialogOpen(true)}
                    >
                      <Info className="h-4 w-4" />
                      View Reports
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Property History Timeline */}
        <Card className="mt-8">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <ClockIcon className="h-5 w-5 text-orange-500" />
                Property History
              </CardTitle>
              <Button
                variant="outline"
                size="sm"
                className="gap-2"
                onClick={() => setProjectWizardOpen(true)}
              >
                Add Project
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {isLoadingJobs ? (
              <div className="space-y-4">
                <Skeleton className="h-24 w-full" />
                <Skeleton className="h-24 w-full" />
              </div>
            ) : sortedYears.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-12">
                <div className="mb-4 rounded-full bg-orange-100 p-3">
                  <ClockIcon className="h-6 w-6 text-orange-600" />
                </div>
                <h3 className="mb-2 font-medium text-lg">No Projects Yet</h3>
                <p className="mb-6 text-center text-muted-foreground">
                  This property doesn't have any completed projects. Start by
                  creating your first project.
                </p>
                <Button onClick={() => setProjectWizardOpen(true)}>
                  Start a New Project
                </Button>
              </div>
            ) : (
              <div className="relative space-y-8">
                <div className="absolute top-0 left-[21px] h-full w-px bg-border" />
                {sortedYears.map((year) => (
                  <div key={year} className="relative">
                    <div className="mb-6 flex items-center gap-3">
                      <div className="relative z-10 h-11 w-11 rounded-full border-2 border-orange-200 bg-orange-100">
                        <span className="-translate-x-1/2 -translate-y-1/2 absolute top-1/2 left-1/2 font-medium text-orange-700">
                          {year.slice(2)}
                        </span>
                      </div>
                      <h3 className="font-medium text-lg">{year}</h3>
                    </div>
                    <div className="ml-[43px] space-y-4">
                      {(jobsByYear?.[year] || [])?.map((job) => (
                        <Link
                          href={`/jobs/${job.id}`}
                          key={job.id}
                          className="block"
                        >
                          <div className="group relative rounded-lg border bg-card p-4 transition-all hover:border-orange-200 hover:shadow-md">
                            <div className="flex items-start justify-between gap-4">
                              <div className="flex-1">
                                <h4 className="font-medium group-hover:text-orange-600">
                                  {job.name}
                                </h4>
                                <p className="mt-1 text-muted-foreground text-sm">
                                  Completed:{" "}
                                  {format(
                                    job.completedAt as Date,
                                    "MMMM d, yyyy",
                                  )}
                                </p>
                              </div>
                              <Badge
                                variant="outline"
                                className={cn(
                                  "bg-orange-50 text-orange-700",
                                  job.status.toLowerCase() === "completed" &&
                                    "bg-green-50 text-green-700",
                                  job.status.toLowerCase() === "in-progress" &&
                                    "bg-blue-50 text-blue-700",
                                )}
                              >
                                {job.status}
                              </Badge>
                            </div>
                            <div className="-translate-y-1/2 absolute top-1/2 right-2 opacity-0 transition-opacity group-hover:opacity-100">
                              <ChevronRight className="h-5 w-5 text-orange-600" />
                            </div>
                          </div>
                        </Link>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Project Wizard */}
      <ProjectWizard
        open={projectWizardOpen}
        onOpenChange={setProjectWizardOpen}
        propertyId={id}
      />

      {/* Image Viewer Dialog */}
      <Dialog open={imageViewerOpen} onOpenChange={setImageViewerOpen}>
        <DialogContent className="sm:max-w-3xl">
          <DialogTitle className="sr-only">Property Image</DialogTitle>
          <div className="relative h-[70vh] w-full overflow-hidden rounded-lg">
            {property?.imageUrl && (
              <Image
                src={property.imageUrl}
                alt={property.name}
                fill
                className="object-contain"
                priority
              />
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Reports Dialog */}
      <Dialog open={reportsDialogOpen} onOpenChange={setReportsDialogOpen}>
        <DialogContent>
          <DialogTitle className="sr-only">Property Reports</DialogTitle>
          <div className="p-4 text-center">
            <h2 className="mb-4 font-semibold text-xl">Property Reports</h2>
            <p className="mb-6 text-muted-foreground">
              Property analytics and reports will be available in a future
              update.
            </p>
            <Button onClick={() => setReportsDialogOpen(false)}>Close</Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
