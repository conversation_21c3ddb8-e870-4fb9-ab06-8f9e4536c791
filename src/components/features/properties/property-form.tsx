"use client";

import {zodResolver} from "@hookform/resolvers/zod";
import {useMutation, useQueryClient} from "@tanstack/react-query";
import {
  ChevronsUpDownIcon,
  HomeIcon,
  ImageIcon,
  MapPinIcon,
} from "lucide-react";
import {useRouter} from "next/navigation";
import {useRef, useState} from "react";
import {type SubmitHandler, useForm} from "react-hook-form";
import {toast} from "sonner";
import states from "states-us";
import {useTRPC} from "@/components/integrations/trpc/client";
import {Button} from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {DrawerClose} from "@/components/ui/drawer";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {Input} from "@/components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {Separator} from "@/components/ui/separator";
import type {Property} from "@/db/schema";
import {type PropertyFormData, propertySchema} from "@/lib/schema";
import {cn} from "@/lib/utils";
import {PropertyImageUploader} from "./enhanced-property-image-uploader";

export function PropertyForm({
                               onboarding,
                               onSuccess,
                               initialData,
                               inDrawer,
                             }: Readonly<{
  onboarding?: boolean;
  onSuccess?: () => void;
  initialData?: Property;
  inDrawer?: boolean;
}>) {
  const router = useRouter();
  const trpc = useTRPC();
  const queryClient = useQueryClient();
  const drawerCloseRef = useRef<HTMLButtonElement>(null);

  const createProperty = useMutation(
    trpc.properties.create.mutationOptions({
      onSuccess: () => {
        queryClient.invalidateQueries({
          queryKey: trpc.properties.list.queryKey(),
        });
      },
    }),
  );

  const updateProperty = useMutation(
    trpc.properties.update.mutationOptions({
      onSuccess: () => {
        queryClient.invalidateQueries({
          queryKey: trpc.properties.list.queryKey(),
        });
      },
    }),
  );

  const [isSubmitting, setIsSubmitting] = useState(false);
  const isEditing = !!initialData;

  const handleImageUploaded = (imageUrl: string) => {
    form.setValue("imageUrl", imageUrl);
  };

  const handleImageRemoved = () => {
    form.setValue("imageUrl", "");
  };

  const form = useForm({
    resolver: zodResolver(propertySchema),
    defaultValues: {
      name: initialData?.name || "",
      imageUrl: initialData?.imageUrl || "",
      address: {
        street: initialData?.address?.street || "",
        city: initialData?.address?.city || "",
        state: initialData?.address?.state || "",
        zip: initialData?.address?.zip || "",
      },
    }
  });

  const onSubmit: SubmitHandler<PropertyFormData> = async (data, e) => {
    e?.preventDefault();
    setIsSubmitting(true);

    try {
      if (isEditing) {
        // Update existing property
        updateProperty.mutate(
          {...data, id: initialData.id},
          {
            onSuccess: () => {
              toast.success("Property updated", {
                description: `${data.name} has been successfully updated.`,
              });

              // Close the drawer if it exists
              if (drawerCloseRef.current) {
                drawerCloseRef.current.click();
              }

              // Call the onSuccess callback if provided
              if (onSuccess) {
                onSuccess();
              }

              router.push("/properties");
              router.refresh();
            },
            onError: (error) => {
              console.error("Error updating property:", error);
              toast.error("Error", {
                description: "Failed to update property. Please try again.",
              });
            },
            onSettled: () => {
              setIsSubmitting(false);
            },
          },
        );
      } else {
        // Create a new property
        createProperty.mutate(
          {
            ...data,
            imageUrl: data.imageUrl || "",
          },
          {
            onSuccess: async () => {
              toast.success("Property created", {
                description: `${data.name} has been successfully created.`,
              });

              // Close the drawer if it exists
              if (drawerCloseRef.current) {
                drawerCloseRef.current.click();
              }

              // Call the onSuccess callback if provided
              if (onSuccess) {
                onSuccess();
              }

              if (!onboarding) {
                router.push("/properties");
                router.refresh();
              }
            },
            onError: (error) => {
              console.error("Error creating property:", error);
              toast.error("Error", {
                description: "Failed to create property. Please try again.",
              });
            },
            onSettled: () => {
              setIsSubmitting(false);
            },
          },
        );
      }
    } catch (error) {
      console.error("Error submitting form:", error);
      setIsSubmitting(false);
    }
  };

  return (
    <div className="overflow-y-scroll p-6">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <div className="mx-auto max-w-2/3">
            <div className="mb-6">
              <div className="mb-4 flex items-center gap-2">
                <HomeIcon className="h-5 w-5 text-orange-500"/>
                <h3 className="font-medium text-lg">Property Details</h3>
              </div>
              <Separator className="mb-6"/>

              <FormField
                control={form.control}
                name="name"
                render={({field}) => (
                  <FormItem className="mb-4">
                    <FormLabel>Property Name</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="e.g. Beach House, Rental Property"
                        className="focus-visible:ring-orange-500"
                      />
                    </FormControl>
                    <FormMessage/>
                  </FormItem>
                )}
              />
            </div>

            <div className="mb-6">
              <div className="mb-4 flex items-center gap-2">
                <MapPinIcon className="h-5 w-5 text-orange-500"/>
                <h3 className="font-medium text-lg">Address</h3>
              </div>
              <Separator className="mb-6"/>

              <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                <FormField
                  control={form.control}
                  name="address.street"
                  render={({field}) => (
                    <FormItem className="md:col-span-3">
                      <FormLabel>Street Address</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          autoComplete="address-line1"
                          placeholder="123 Main St"
                          className="focus-visible:ring-orange-500"
                        />
                      </FormControl>
                      <FormMessage/>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="address.city"
                  render={({field}) => (
                    <FormItem>
                      <FormLabel>City</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          autoComplete="address-level2"
                          placeholder="San Francisco"
                          className="focus-visible:ring-orange-500"
                        />
                      </FormControl>
                      <FormMessage/>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="address.state"
                  render={({field}) => (
                    <FormItem>
                      <FormLabel>State</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant="outline"
                              // biome-ignore lint/a11y/useSemanticElements: Custom combobox
                              role="combobox"
                              className={cn(
                                "w-full justify-between",
                                !field.value && "text-muted-foreground",
                              )}
                            >
                              {field.value
                                ? states.find(
                                  (state) =>
                                    state.abbreviation === field.value,
                                )?.name
                                : "Select state"}
                              <ChevronsUpDownIcon className="ml-auto h-4 w-4 opacity-50"/>
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-full p-0">
                          <Command>
                            <CommandInput
                              placeholder="Search state..."
                              className="h-9"
                            />
                            <CommandList>
                              <CommandEmpty>No state found.</CommandEmpty>
                              <CommandGroup>
                                {states.map((state) => (
                                  <CommandItem
                                    value={state.abbreviation}
                                    key={state.abbreviation}
                                    onSelect={(value) => {
                                      form.setValue("address.state", value);
                                    }}
                                  >
                                    {state.name}
                                  </CommandItem>
                                ))}
                              </CommandGroup>
                            </CommandList>
                          </Command>
                        </PopoverContent>
                      </Popover>
                      <FormMessage/>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="address.zip"
                  render={({field}) => (
                    <FormItem>
                      <FormLabel>ZIP Code</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          autoComplete="postal-code"
                          placeholder="94103"
                          className="focus-visible:ring-orange-500"
                        />
                      </FormControl>
                      <FormMessage/>
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <div className="mb-6">
              <div className="mb-4 flex items-center gap-2">
                <ImageIcon className="h-5 w-5 text-orange-500"/>
                <h3 className="font-medium text-lg">Property Image</h3>
              </div>
              <Separator className="mb-6"/>

              <FormField
                control={form.control}
                name="imageUrl"
                render={({field}) => (
                  <FormItem>
                    <FormLabel>Property Image</FormLabel>
                    <FormControl>
                      <PropertyImageUploader
                        initialImageUrl={field.value}
                        onImageUploaded={handleImageUploaded}
                        onImageRemoved={handleImageRemoved}
                      />
                    </FormControl>
                    <FormMessage/>
                  </FormItem>
                )}
              />
            </div>

            <div className="mt-8 flex justify-end gap-4 pb-4">
              {inDrawer && (
                <DrawerClose ref={drawerCloseRef} asChild>
                  <Button type="button" variant="outline">
                    Cancel
                  </Button>
                </DrawerClose>
              )}

              <Button
                type="submit"
                disabled={isSubmitting || (!isEditing && !form.getValues("imageUrl"))}
                className="bg-orange-600 hover:bg-orange-700"
              >
                {isSubmitting
                  ? isEditing
                    ? "Updating..."
                    : "Creating..."
                  : isEditing
                    ? "Update Property"
                    : "Create Property"}
              </Button>
            </div>
          </div>
        </form>
      </Form>
    </div>
  );
}
