"use client";

import { Icon } from "leaflet";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "react-leaflet";
import type { Job, Organization } from "@/db/schema";

import "leaflet/dist/leaflet.css";
import defaultIconPng from "leaflet/dist/images/marker-icon.png";
import defaultShadowPng from "leaflet/dist/images/marker-shadow.png";

export interface ProjectsMapProps {
  projects: Job[] | null | undefined;
  organization: Organization;
}

const defaultIcon = new Icon({
  //@ts-ignore
  iconUrl: defaultIconPng,
  iconSize: [25, 41],
  iconAnchor: [12, 41],
  popupAnchor: [1, -34],
  shadowSize: [41, 41],
  //@ts-ignore
  shadowUrl: defaultShadowPng,
});

export default function ProjectsMap({
  projects,
  organization,
}: ProjectsMapProps) {
  const orgLocation = organization.address?.location;

  return (
    <>
      <MapContainer
        center={[orgLocation?.x || 0, orgLocation?.y || 0]}
        zoom={10}
        className="h-[60vh]"
      >
        <TileLayer
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
          attribution={`&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors`}
        />
        <Marker
          position={[orgLocation?.x || 0, orgLocation?.y || 0]}
          icon={defaultIcon}
        >
          <Popup>
            <b>{organization?.name}</b>
            <br />
            Your location
          </Popup>
        </Marker>
        {projects?.map((project) => {
          if (project.property?.address?.location) {
            const projectLocation = project.property.address.location;
            return (
              <Marker
                key={project.id}
                position={[projectLocation.x, projectLocation.y]}
                icon={defaultIcon}
              >
                <Popup>
                  <b>{project.name}</b>
                  <br />
                  {project.property.name}
                  <br />
                  {project.distance
                    ? `Distance: ${Number(project.distance).toFixed(1)} miles`
                    : ""}
                  <br />
                  Budget: ${project.budget}
                </Popup>
              </Marker>
            );
          }
          return null;
        })}
      </MapContainer>
    </>
  );
}
