"use client";

import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";

interface JackIntroductionProps {
  userName: string;
  userRole: string;
  onQuickAction?: (action: string) => void;
}

export function JackIntroduction({
  userName,
  userRole,
  onQuickAction,
}: JackIntroductionProps) {
  const getQuickActions = () => {
    switch (userRole) {
      case "homeowner":
        return [
          { label: "Create a new project", action: "create-project" },
          { label: "Find contractors", action: "find-contractors" },
          { label: "Get project timeline", action: "project-timeline" },
        ];
      case "contractor":
        return [
          { label: "Find relevant jobs", action: "find-jobs" },
          { label: "Analyze competition", action: "analyze-competition" },
          { label: "Check performance", action: "check-performance" },
        ];
      case "admin":
        return [
          { label: "View platform insights", action: "platform-insights" },
          { label: "Check user stats", action: "user-stats" },
          { label: "Manage users", action: "manage-users" },
        ];
      default:
        return [
          { label: "Explore features", action: "explore-features" },
          { label: "Get help", action: "get-help" },
        ];
    }
  };

  const quickActions = getQuickActions();

  return (
    <Card className="border-l-4 border-l-tradecrews-blue bg-gradient-to-br from-tradecrews-blue-50 via-background to-tradecrews-orange-50 dark:from-tradecrews-blue-950/30 dark:via-background dark:to-tradecrews-orange-950/30">
      <CardContent className="p-6">
        <div className="space-y-4">
          {/* Jack's Avatar and Introduction */}
          <div className="flex items-start gap-4">
            <div className="relative">
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-tradecrews-blue text-white">
                <Bot className="h-6 w-6" />
              </div>
              <div className="-top-1 -right-1 absolute flex h-5 w-5 items-center justify-center rounded-full bg-tradecrews-orange">
                <Sparkles className="h-3 w-3 text-white" />
              </div>
            </div>
            <div className="flex-1">
              <div className="mb-2 flex items-center gap-2">
                <h3 className="font-bold text-foreground text-lg">
                  Hi {userName}! 👋
                </h3>
                <Badge
                  variant="secondary"
                  className="bg-tradecrews-orange-100 text-tradecrews-orange-700 dark:bg-tradecrews-orange-900/50 dark:text-tradecrews-orange-300"
                >
                  AI Assistant
                </Badge>
              </div>
              <p className="text-muted-foreground leading-relaxed">
                I'm <strong className="text-foreground">Jack</strong>, your AI
                assistant here at TradeCrews! I'm excited to help you
                {userRole === "homeowner" &&
                  " manage your home improvement projects and connect with the best contractors."}
                {userRole === "contractor" &&
                  " find great projects, optimize your bids, and grow your business."}
                {userRole === "admin" &&
                  " manage the platform, analyze user data, and maintain system health."}
              </p>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="space-y-3">
            <h4 className="flex items-center gap-2 font-semibold text-foreground text-sm">
              <ArrowRight className="h-4 w-4 text-tradecrews-orange" />
              Here's how I can help you today:
            </h4>
            <div className="grid gap-2">
              {quickActions.map((action, index) => (
                <Button
                  key={action.action}
                  variant="outline"
                  size="sm"
                  onClick={() => onQuickAction?.(action.action)}
                  className="justify-start border-tradecrews-blue/20 text-left hover:border-tradecrews-blue hover:bg-tradecrews-blue-50 dark:border-tradecrews-blue/30 dark:hover:border-tradecrews-blue-400 dark:hover:bg-tradecrews-blue-950/50"
                >
                  <span className="mr-2 flex h-6 w-6 items-center justify-center rounded-full bg-tradecrews-blue-100 font-medium text-tradecrews-blue-700 text-xs dark:bg-tradecrews-blue-900/50 dark:text-tradecrews-blue-300">
                    {index + 1}
                  </span>
                  {action.label}
                </Button>
              ))}
            </div>
          </div>

          {/* Call to Action */}
          <div className="rounded-lg border border-tradecrews-orange/20 bg-card/60 p-4 dark:border-tradecrews-orange/30 dark:bg-card/40">
            <p className="text-center text-muted-foreground text-sm">
              💬{" "}
              <strong className="text-foreground">Just ask me anything!</strong>{" "}
              I can help with projects, contractors, scheduling, and much more.
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
