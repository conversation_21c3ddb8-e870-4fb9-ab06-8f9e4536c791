"use client";

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { format } from "date-fns";
import {
  AlertCircle,
  Archive,
  Bot,
  Check,
  Clock,
  Copy,
  Download,
  Edit,
  MessageSquare,
  Mic,
  MicOff,
  MoreVertical,
  Paperclip,
  Pin,
  Reply,
  Search,
  Send,
  Settings,
  Smile,
  Trash2,
  Users,
  Volume2,
  VolumeX,
  Zap,
} from "lucide-react";
import { useRouter } from "next/navigation";
import type React from "react";
import { useCallback, useEffect, useRef, useState } from "react";
import { useTRPC } from "@/components/integrations/trpc/client";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Textarea } from "@/components/ui/textarea";
import type { Message } from "@/db/schema";
import {
  type ChatContext,
  generateChatSuggestions,
  getContextualSuggestions,
  type SuggestedQuestion,
} from "@/lib/ai/chat-suggestions";
import { useSession } from "@/lib/auth-client";
import {
  sendTypingIndicator,
  subscribeToChatChannel,
  unsubscribeFromChatChannel,
  updatePresence,
} from "@/lib/pusher-client";
import { cn } from "@/lib/utils";

interface PusherChatProps {
  bidId?: string;
  jobId?: string;
  userId: string;
  className?: string;
  showHeader?: boolean;
  showActions?: boolean;
  maxHeight?: string;
}

interface MessageReaction {
  emoji: string;
  count: number;
  users: string[];
  hasReacted: boolean;
}

interface EnhancedMessage extends Message {
  reactions?: MessageReaction[];
  isEdited?: boolean;
  editedAt?: Date;
  replyTo?: string;
  isPinned?: boolean;
  readBy?: string[];
}

export function PusherChat({
  bidId,
  jobId,
  userId,
  className,
  showHeader = true,
  showActions = true,
  maxHeight = "h-96",
}: PusherChatProps) {
  const trpc = useTRPC();
  const queryClient = useQueryClient();
  const [newMessage, setNewMessage] = useState("");
  const [chatId, setChatId] = useState<string>();
  const [typingUsers, setTypingUsers] = useState<Map<string, string>>(
    new Map(),
  );
  const [onlineUsers, setOnlineUsers] = useState<Set<string>>(new Set());
  const [suggestedQuestions, setSuggestedQuestions] = useState<
    SuggestedQuestion[]
  >([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [showSearch, setShowSearch] = useState(false);
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [editingMessage, setEditingMessage] = useState<string | null>(null);
  const [editText, setEditText] = useState("");
  const [isRecording, setIsRecording] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [selectedMessages, setSelectedMessages] = useState<Set<string>>(
    new Set(),
  );
  const [isMultiSelect, setIsMultiSelect] = useState(false);

  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messageCountRef = useRef<number>(0);
  const router = useRouter();
  const { data: session } = useSession();
  const user = session?.user;
  const isHomeowner = user?.role === "homeowner";
  const isProfessional = user?.role === "contractor";

  // Enhanced message reactions
  const commonEmojis = ["👍", "❤️", "😊", "😮", "😢", "😡", "🎉", "🔥"];

  // Fetch messages with enhanced data
  const { data: messages, isLoading } = useQuery(
    trpc.messages.listMessages.queryOptions({
      bidId,
      jobId,
    }),
  );

  // Fetch job/bid data for context
  const { data: jobData } = useQuery({
    ...trpc.projects.getById.queryOptions({ id: jobId || "" }),
    enabled: !!jobId,
  });

  const { data: bidData } = useQuery({
    ...trpc.bids.getById.queryOptions({ id: bidId || "" }),
    enabled: !!bidId,
  });

  const createMessageMutation = useMutation(
    trpc.messages.createMessage.mutationOptions({
      onSuccess: () => {
        setNewMessage("");
        setReplyingTo(null);
      },
    }),
  );

  const ensureChatMutation = useMutation(
    trpc.messages.ensureChat.mutationOptions({
      onSuccess: (chat) => {
        setChatId(chat?.id);
      },
    }),
  );

  // Enhanced message handlers
  const handlePusherMessage = useCallback(
    (data: {
      content: string;
      isCommand?: boolean;
      commandData?: string;
      messageId?: string;
      type?: "message" | "reaction" | "edit" | "delete";
    }) => {
      queryClient.invalidateQueries({
        queryKey: trpc.messages.listMessages.queryKey({ bidId, jobId }),
      });

      // Handle different message types
      if (data.type === "reaction") {
        // Handle reaction updates
        queryClient.invalidateQueries({
          queryKey: trpc.messages.listMessages.queryKey({ bidId, jobId }),
        });
      } else if (data.type === "edit") {
        // Handle message edits
        queryClient.invalidateQueries({
          queryKey: trpc.messages.listMessages.queryKey({ bidId, jobId }),
        });
      }

      if (data.isCommand && data.content.includes("[Command executed:")) {
        const commandData = data.commandData
          ? JSON.parse(data.commandData)
          : null;

        if (bidId) {
          queryClient.invalidateQueries({
            queryKey: trpc.bids.getById.queryKey({ id: bidId }),
          });
        }
        if (jobId) {
          queryClient.invalidateQueries({
            queryKey: trpc.projects.getById.queryKey({ id: jobId }),
          });
        }
        router.refresh();
      }
    },
    [queryClient, bidId, jobId, router, trpc],
  );

  const handlePusherTyping = useCallback(
    (data: { userId: string; isTyping: boolean; userName?: string }) => {
      if (data.userId === userId) return;

      setTypingUsers((prev) => {
        const newMap = new Map(prev);
        if (data.isTyping) {
          newMap.set(data.userId, data.userName || data.userId);
        } else {
          newMap.delete(data.userId);
        }
        return newMap;
      });
    },
    [userId],
  );

  const handlePusherPresence = useCallback(
    (data: { userId: string; status: string }) => {
      if (data.userId === userId) return;

      setOnlineUsers((prev) => {
        const newSet = new Set(prev);
        if (data.status === "online") {
          newSet.add(data.userId);
        } else {
          newSet.delete(data.userId);
        }
        return newSet;
      });
    },
    [userId],
  );

  // Set up chat and Pusher subscription
  useEffect(() => {
    if (messages && messages.length > 0) {
      setChatId(messages[0]?.chatId);
    } else if (!isLoading && !chatId) {
      ensureChatMutation.mutate({ bidId, jobId });
    }
  }, [messages, isLoading, chatId, bidId, jobId, ensureChatMutation]);

  useEffect(() => {
    if (!chatId) return;

    subscribeToChatChannel(chatId, {
      onMessage: handlePusherMessage,
      onTyping: handlePusherTyping,
      onPresence: handlePusherPresence,
      onSubscriptionSucceeded: () => {
        console.log("Successfully subscribed to enhanced chat channel");
        updatePresence(chatId, userId, "online");
      },
      onSubscriptionError: (error) => {
        console.error("Failed to subscribe to enhanced chat channel:", error);
      },
    });

    updatePresence(chatId, userId, "online");

    return () => {
      updatePresence(chatId, userId, "offline");
      unsubscribeFromChatChannel(chatId);
    };
  }, [
    chatId,
    handlePusherMessage,
    handlePusherTyping,
    handlePusherPresence,
    userId,
  ]);

  // Auto-scroll to bottom
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  });

  // Generate AI-powered suggestions when messages change
  useEffect(() => {
    const generateSuggestions = async () => {
      if (!messages || messages.length === 0) {
        // Generate initial suggestions
        const context: ChatContext = {
          jobId,
          bidId,
          userRole: user?.role || "homeowner",
          projectType: jobData?.name || undefined,
          projectStatus: jobData?.status || undefined,
          bidStatus: bidData?.status || undefined,
          contractorName: bidData?.organization?.name || undefined,
          homeownerName: user?.name || undefined,
        };

        try {
          const suggestions = await generateChatSuggestions(context);
          setSuggestedQuestions(suggestions);
        } catch (error) {
          console.error("Error generating initial suggestions:", error);
          // Use contextual fallback
          const fallbackSuggestions = getContextualSuggestions(
            [],
            user?.role || "homeowner",
          );
          setSuggestedQuestions(fallbackSuggestions);
        }
      } else if (messages.length > 0) {
        // Generate contextual suggestions based on recent messages
        const recentMessages = messages.slice(-5).map((m) => ({
          content: m.content,
          senderType: m.senderType,
          createdAt: m.createdAt,
        }));

        const contextualSuggestions = getContextualSuggestions(
          recentMessages,
          user?.role || "homeowner",
        );
        setSuggestedQuestions(contextualSuggestions);

        // Optionally generate AI suggestions for complex conversations
        if (messages.length > 10) {
          const context: ChatContext = {
            jobId,
            bidId,
            userRole: user?.role || "homeowner",
            projectType: jobData?.name || undefined,
            projectStatus: jobData?.status || undefined,
            bidStatus: bidData?.status || undefined,
            contractorName: bidData?.organization?.name || undefined,
            homeownerName: user?.name || undefined,
            recentMessages,
          };

          try {
            const aiSuggestions = await generateChatSuggestions(context);
            if (aiSuggestions.length > 0) {
              setSuggestedQuestions(aiSuggestions);
            }
          } catch (error) {
            console.error("Error generating AI suggestions:", error);
            // Keep contextual suggestions as fallback
          }
        }
      }
    };

    generateSuggestions();
  }, [messages, jobData, bidData, user?.role, jobId, bidId, user?.name]);

  // Enhanced typing handler
  const isAlreadyTyping = useRef(false);
  const handleTyping = useCallback(() => {
    if (!chatId) return;

    if (!isAlreadyTyping.current) {
      const userName = user?.name || userId;
      sendTypingIndicator(chatId, userId, true, userName);
      isAlreadyTyping.current = true;
    }

    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    typingTimeoutRef.current = setTimeout(() => {
      sendTypingIndicator(chatId, userId, false);
      isAlreadyTyping.current = false;
    }, 3000);
  }, [chatId, userId, user]);

  // Enhanced message sending
  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newMessage.trim()) return;

    try {
      const [firstName, lastName] = user?.name.split(" ") || [];
      const userInitials = user
        ? `${firstName?.[0] || ""}${lastName?.[0] || ""}`.toUpperCase()
        : userId.slice(0, 2).toUpperCase();

      await createMessageMutation.mutateAsync({
        bidId,
        jobId,
        content: newMessage.trim(),
        senderInitials: userInitials,
        senderAvatarUrl: user?.image || "",
        // replyToId: replyingTo, // TODO: Add reply support to mutation
      });
    } catch (error) {
      console.error("Failed to send message:", error);
    }
  };

  // Message actions
  const handleReaction = (messageId: string, emoji: string) => {
    // TODO: Implement reaction system
    console.log("Adding reaction:", emoji, "to message:", messageId);
  };

  const handleReply = (messageId: string) => {
    setReplyingTo(messageId);
    // Focus on input
  };

  const handleEdit = (messageId: string, currentContent: string) => {
    setEditingMessage(messageId);
    setEditText(currentContent);
  };

  const handleDelete = (messageId: string) => {
    // TODO: Implement message deletion
    console.log("Deleting message:", messageId);
  };

  const handlePin = (messageId: string) => {
    // TODO: Implement message pinning
    console.log("Pinning message:", messageId);
  };

  const isOwnMessage = (senderId: string) => senderId === userId;

  // Enhanced message rendering
  const renderEnhancedMessage = (message: EnhancedMessage) => {
    const isOwn = isOwnMessage(message.senderId);

    return (
      <div
        key={message.id}
        className={cn(
          "group mb-4 flex",
          isOwn ? "justify-end" : "justify-start",
          selectedMessages.has(message.id) &&
            "rounded-lg bg-blue-50 p-2 dark:bg-blue-950/20",
        )}
      >
        <div
          className={cn(
            "max-w-[80%] space-y-1",
            isOwn ? "items-end" : "items-start",
          )}
        >
          {/* Reply indicator */}
          {message.replyTo && (
            <div className="text-muted-foreground text-xs opacity-70">
              <Reply className="mr-1 inline h-3 w-3" />
              Replying to message
            </div>
          )}

          {/* Message bubble */}
          <div
            className={cn(
              "relative rounded-lg p-3 transition-all",
              isOwn ? "bg-tradecrews-blue text-white" : "border bg-card",
              message.isPinned && "ring-2 ring-tradecrews-orange/50",
            )}
          >
            {/* Message header */}
            <div className="mb-1 flex items-center gap-2">
              <Avatar className="h-6 w-6">
                {message.senderAvatarUrl && (
                  <AvatarImage src={message.senderAvatarUrl} />
                )}
                <AvatarFallback>
                  {message.senderInitials || message.senderId.slice(0, 2)}
                </AvatarFallback>
              </Avatar>
              <span
                className={cn(
                  "font-medium text-xs",
                  isOwn ? "text-white/90" : "text-foreground",
                )}
              >
                {message.senderType === "homeowner"
                  ? "Homeowner"
                  : "Professional"}
              </span>
              <span
                className={cn(
                  "text-xs",
                  isOwn ? "text-white/70" : "text-muted-foreground",
                )}
              >
                {format(message.createdAt, "h:mm a")}
              </span>
              {message.isEdited && (
                <span
                  className={cn(
                    "text-xs italic",
                    isOwn ? "text-white/70" : "text-muted-foreground",
                  )}
                >
                  (edited)
                </span>
              )}
              {message.isPinned && (
                <Pin
                  className={cn(
                    "h-3 w-3",
                    isOwn ? "text-white/70" : "text-tradecrews-orange",
                  )}
                />
              )}
            </div>

            {/* Message content */}
            {message.isCommand ? (
              renderCommandMessage(message)
            ) : (
              <p
                className={cn(
                  "whitespace-pre-wrap",
                  isOwn ? "text-white" : "text-foreground",
                )}
              >
                {message.content}
              </p>
            )}

            {/* Message reactions */}
            {message.reactions && message.reactions.length > 0 && (
              <div className="mt-2 flex flex-wrap gap-1">
                {message.reactions.map((reaction) => (
                  <Button
                    key={reaction.emoji}
                    variant="ghost"
                    size="sm"
                    className={cn(
                      "h-6 px-2 text-xs",
                      reaction.hasReacted &&
                        "bg-tradecrews-orange/20 text-tradecrews-orange",
                    )}
                    onClick={() => handleReaction(message.id, reaction.emoji)}
                  >
                    {reaction.emoji} {reaction.count}
                  </Button>
                ))}
              </div>
            )}

            {/* Message actions (show on hover) */}
            <div className="-top-2 absolute right-2 hidden group-hover:flex">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 border bg-background p-0 shadow-sm"
                  >
                    <MoreVertical className="h-3 w-3" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => handleReply(message.id)}>
                    <Reply className="mr-2 h-4 w-4" />
                    Reply
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() =>
                      navigator.clipboard.writeText(message.content)
                    }
                  >
                    <Copy className="mr-2 h-4 w-4" />
                    Copy
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handlePin(message.id)}>
                    <Pin className="mr-2 h-4 w-4" />
                    {message.isPinned ? "Unpin" : "Pin"}
                  </DropdownMenuItem>
                  {isOwn && (
                    <>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        onClick={() => handleEdit(message.id, message.content)}
                      >
                        <Edit className="mr-2 h-4 w-4" />
                        Edit
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => handleDelete(message.id)}
                        className="text-red-600"
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        Delete
                      </DropdownMenuItem>
                    </>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          {/* Quick reactions */}
          <div className="hidden gap-1 group-hover:flex">
            {commonEmojis.slice(0, 4).map((emoji) => (
              <Button
                key={emoji}
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0 text-xs hover:bg-tradecrews-orange/20"
                onClick={() => handleReaction(message.id, emoji)}
              >
                {emoji}
              </Button>
            ))}
          </div>
        </div>
      </div>
    );
  };

  const renderCommandMessage = (message: Message) => {
    if (!message.isCommand) return null;

    let commandData = null;
    try {
      if (message.commandData) {
        commandData = JSON.parse(message.commandData);
      }
    } catch (e) {
      console.error("Failed to parse command data:", e);
    }

    const isSuccess = message.content.includes("[Command executed:");
    const icon = isSuccess ? (
      <Check className="h-4 w-4 text-green-500" />
    ) : (
      <AlertCircle className="h-4 w-4 text-red-500" />
    );

    return (
      <div className="flex items-center gap-2 text-sm">
        {icon}
        <span>
          {message.content
            .replace(/\[(Command executed|Command failed): /, "")
            .replace(/\]$/, "")}
        </span>
        {commandData && (
          <div className="ml-2 rounded bg-muted px-2 py-1 text-xs">
            {commandData.amount && <span>${commandData.amount}</span>}
            {commandData.estimatedDuration && (
              <span>
                <Clock className="mr-1 inline h-3 w-3" />
                {commandData.estimatedDuration} days
              </span>
            )}
          </div>
        )}
      </div>
    );
  };

  // Filter messages based on search
  const filteredMessages =
    messages?.filter(
      (message) =>
        !searchQuery ||
        message.content.toLowerCase().includes(searchQuery.toLowerCase()),
    ) || [];

  if (isLoading) {
    return (
      <div className="flex h-96 items-center justify-center">
        <div className="text-muted-foreground">Loading enhanced chat...</div>
      </div>
    );
  }

  return (
    <Card className={cn("flex flex-col", className)}>
      {/* Enhanced Header */}
      {showHeader && (
        <CardHeader className="border-b bg-gradient-to-r from-tradecrews-blue-50 to-tradecrews-orange-50 dark:from-tradecrews-blue-950/30 dark:to-tradecrews-orange-950/30">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <MessageSquare className="h-5 w-5 text-tradecrews-blue" />
              Enhanced Chat
            </CardTitle>
            <div className="flex items-center gap-2">
              {onlineUsers.size > 0 && (
                <Badge variant="secondary" className="flex items-center gap-1">
                  <Users className="h-3 w-3" />
                  {onlineUsers.size + 1} online
                </Badge>
              )}
              {showActions && (
                <div className="flex items-center gap-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowSearch(!showSearch)}
                  >
                    <Search className="h-4 w-4" />
                  </Button>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <Settings className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => setIsMuted(!isMuted)}>
                        {isMuted ? (
                          <Volume2 className="mr-2 h-4 w-4" />
                        ) : (
                          <VolumeX className="mr-2 h-4 w-4" />
                        )}
                        {isMuted ? "Unmute" : "Mute"} notifications
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Archive className="mr-2 h-4 w-4" />
                        Archive chat
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Download className="mr-2 h-4 w-4" />
                        Export chat
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              )}
            </div>
          </div>

          {/* Search bar */}
          {showSearch && (
            <div className="mt-3">
              <Input
                placeholder="Search messages..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="h-8"
              />
            </div>
          )}
        </CardHeader>
      )}

      {/* Enhanced Messages */}
      <CardContent className="flex-1 p-0">
        <ScrollArea className={cn("p-4", maxHeight)}>
          <div className="space-y-2">
            {filteredMessages.map((message) =>
              renderEnhancedMessage(message as EnhancedMessage),
            )}

            {/* Enhanced typing indicator */}
            {typingUsers.size > 0 && (
              <div className="flex justify-start">
                <div className="flex items-center gap-2 rounded-lg bg-muted p-3">
                  <div className="flex space-x-1">
                    <div className="h-2 w-2 animate-bounce rounded-full bg-tradecrews-blue" />
                    <div
                      className="h-2 w-2 animate-bounce rounded-full bg-tradecrews-blue"
                      style={{ animationDelay: "0.1s" }}
                    />
                    <div
                      className="h-2 w-2 animate-bounce rounded-full bg-tradecrews-blue"
                      style={{ animationDelay: "0.2s" }}
                    />
                  </div>
                  <span className="text-muted-foreground text-sm">
                    {Array.from(typingUsers.values()).join(", ")}{" "}
                    {typingUsers.size === 1 ? "is" : "are"} typing...
                  </span>
                </div>
              </div>
            )}

            <div ref={messagesEndRef} />
          </div>
        </ScrollArea>
      </CardContent>

      {/* Enhanced Message Input */}
      <div className="border-t bg-background p-4">
        {/* Reply indicator */}
        {replyingTo && (
          <div className="mb-2 flex items-center gap-2 rounded bg-muted p-2 text-sm">
            <Reply className="h-4 w-4 text-tradecrews-blue" />
            <span>Replying to message</span>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setReplyingTo(null)}
              className="ml-auto h-6 w-6 p-0"
            >
              ×
            </Button>
          </div>
        )}

        {/* AI-Powered Suggested questions */}
        {suggestedQuestions.length > 0 && (
          <div className="mb-3 space-y-2">
            <div className="flex items-center gap-2">
              <Bot className="h-4 w-4 text-tradecrews-blue" />
              <p className="font-medium text-muted-foreground text-xs">
                Jack suggests asking:
              </p>
            </div>
            <div className="flex flex-wrap gap-2">
              {suggestedQuestions.slice(0, 4).map((suggestion, index) => (
                <Badge
                  key={`${suggestion.text}-${index}`}
                  variant="outline"
                  className={cn(
                    "cursor-pointer px-3 py-1.5 text-xs transition-all hover:scale-105",
                    suggestion.priority === "high"
                      ? "border-tradecrews-orange/50 bg-tradecrews-orange/5 hover:border-tradecrews-orange hover:bg-tradecrews-orange/10"
                      : suggestion.priority === "medium"
                        ? "border-tradecrews-blue/50 bg-tradecrews-blue/5 hover:border-tradecrews-blue hover:bg-tradecrews-blue/10"
                        : "hover:bg-accent hover:text-accent-foreground",
                  )}
                  onClick={() => setNewMessage(suggestion.text)}
                  title={suggestion.context}
                >
                  <span className="mr-1">
                    {suggestion.category === "timeline" && "⏰"}
                    {suggestion.category === "budget" && "💰"}
                    {suggestion.category === "materials" && "🔧"}
                    {suggestion.category === "logistics" && "📋"}
                    {suggestion.category === "project" && "🏗️"}
                    {suggestion.category === "clarification" && "❓"}
                  </span>
                  {suggestion.text}
                </Badge>
              ))}
            </div>
            {suggestedQuestions.length > 4 && (
              <Button
                variant="ghost"
                size="sm"
                className="h-6 text-tradecrews-blue text-xs hover:text-tradecrews-blue-600"
                onClick={() => {
                  // Show more suggestions or cycle through them
                  const nextSuggestions = suggestedQuestions.slice(4, 8);
                  if (nextSuggestions.length > 0) {
                    setSuggestedQuestions([
                      ...nextSuggestions,
                      ...suggestedQuestions.slice(0, 4),
                    ]);
                  }
                }}
              >
                <Zap className="mr-1 h-3 w-3" />
                Show {suggestedQuestions.length - 4} more suggestions
              </Button>
            )}
          </div>
        )}

        {/* Message input form */}
        <form onSubmit={handleSendMessage} className="space-y-2">
          <div className="flex gap-2">
            <div className="relative flex-1">
              <Textarea
                value={newMessage}
                onChange={(e) => {
                  setNewMessage(e.target.value);
                  handleTyping();
                }}
                placeholder="Type a message..."
                disabled={createMessageMutation.isPending}
                className="max-h-32 min-h-[40px] resize-none pr-12"
                onKeyDown={(e) => {
                  if (e.key === "Enter" && !e.shiftKey) {
                    e.preventDefault();
                    handleSendMessage(e);
                  }
                }}
              />

              {/* Input actions */}
              <div className="absolute right-2 bottom-2 flex items-center gap-1">
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0"
                  onClick={() => setShowEmojiPicker(!showEmojiPicker)}
                >
                  <Smile className="h-4 w-4" />
                </Button>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0"
                >
                  <Paperclip className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <div className="flex flex-col gap-1">
              <Button
                type="submit"
                size="sm"
                disabled={!newMessage.trim() || createMessageMutation.isPending}
                className="bg-tradecrews-blue hover:bg-tradecrews-blue-600"
              >
                <Send className="h-4 w-4" />
              </Button>
              <Button
                type="button"
                variant="outline"
                size="sm"
                className={cn(
                  "transition-colors",
                  isRecording && "border-red-300 bg-red-100 text-red-600",
                )}
                onClick={() => setIsRecording(!isRecording)}
              >
                {isRecording ? (
                  <MicOff className="h-4 w-4" />
                ) : (
                  <Mic className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>

          {/* Emoji picker */}
          {showEmojiPicker && (
            <div className="rounded border bg-background p-2 shadow-lg">
              <div className="grid grid-cols-8 gap-1">
                {commonEmojis.map((emoji) => (
                  <Button
                    key={emoji}
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0"
                    onClick={() => {
                      setNewMessage(newMessage + emoji);
                      setShowEmojiPicker(false);
                    }}
                  >
                    {emoji}
                  </Button>
                ))}
              </div>
            </div>
          )}
        </form>
      </div>
    </Card>
  );
}
