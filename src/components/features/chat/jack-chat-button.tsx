"use client";

import { MessageCircle } from "lucide-react";
import Image from "next/image";
import { useState } from "react";
import jackAvatar from "@/assets/images/jack.png";
import { PopupChat } from "@/components/features/chat";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useSession } from "@/lib/auth-client";
import { cn } from "@/lib/utils";

export interface JackChatButtonProps {
  variant?: "fab" | "actionbar" | "button";
  size?: "sm" | "md" | "lg";
  className?: string;
  showLabel?: boolean;
  jobId?: string;
  bidId?: string;
}

export function JackChatButton({
  variant = "actionbar",
  size = "md",
  className,
  showLabel = false,
  jobId,
  bidId,
}: JackChatButtonProps) {
  const [isOpen, setIsOpen] = useState(false);
  const { data: session } = useSession();
  const user = session?.user;

  const sizeClasses = {
    sm: "h-8 w-8",
    md: "h-12 w-12",
    lg: "h-14 w-14",
  };

  const avatarSizeClasses = {
    sm: "h-6 w-6",
    md: "h-8 w-8",
    lg: "h-10 w-10",
  };

  if (variant === "fab") {
    return (
      <>
        <Button
          onClick={() => setIsOpen(true)}
          className={cn(
            "fixed right-6 bottom-6 z-50 rounded-full p-0 shadow-lg",
            sizeClasses[size],
            className,
          )}
          variant="default"
        >
          <Image
            src={jackAvatar}
            alt="Chat with Jack"
            className={cn("rounded-full", avatarSizeClasses[size])}
            width={size === "sm" ? 24 : size === "md" ? 32 : 40}
            height={size === "sm" ? 24 : size === "md" ? 32 : 40}
          />
        </Button>

        <Dialog open={isOpen} onOpenChange={setIsOpen}>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Image
                  src={jackAvatar}
                  alt="Jack"
                  className="h-6 w-6 rounded-full"
                  width={24}
                  height={24}
                />
                Chat with Jack
              </DialogTitle>
              <DialogDescription>
                Get help with your project from our AI assistant.
              </DialogDescription>
            </DialogHeader>
            <div className="h-96">
              <PopupChat
                title="Chat with Jack"
                supportName="Jack"
                supportAvatar={jackAvatar.src}
                userName={user?.name || "there"}
                userAvatar={user?.image}
                userRole={user?.role || "homeowner"}
                className="h-full"
              />
            </div>
          </DialogContent>
        </Dialog>
      </>
    );
  }

  if (variant === "actionbar") {
    return (
      <>
        <Button
          onClick={() => setIsOpen(true)}
          variant="ghost"
          className={cn(
            "flex flex-col items-center gap-1 rounded-full p-2",
            sizeClasses[size],
            className,
          )}
        >
          <Image
            src={jackAvatar}
            alt="Jack"
            className={cn("rounded-full", avatarSizeClasses[size])}
            width={size === "sm" ? 24 : size === "md" ? 32 : 40}
            height={size === "sm" ? 24 : size === "md" ? 32 : 40}
          />
          {showLabel && <span className="text-xs">Jack</span>}
        </Button>

        <Dialog open={isOpen} onOpenChange={setIsOpen}>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Image
                  src={jackAvatar}
                  alt="Jack"
                  className="h-6 w-6 rounded-full"
                  width={24}
                  height={24}
                />
                Chat with Jack
              </DialogTitle>
              <DialogDescription>
                Get help with your project from our AI assistant.
              </DialogDescription>
            </DialogHeader>
            <div className="h-96">
              <PopupChat
                title="Chat with Jack"
                supportName="Jack"
                supportAvatar={jackAvatar.src}
                userName={user?.name || "there"}
                userAvatar={user?.image}
                userRole={user?.role || "homeowner"}
                className="h-full"
              />
            </div>
          </DialogContent>
        </Dialog>
      </>
    );
  }

  // Default button variant
  return (
    <>
      <Button
        onClick={() => setIsOpen(true)}
        variant="outline"
        className={cn("flex items-center gap-2", className)}
      >
        <Image
          src={jackAvatar}
          alt="Jack"
          className="h-5 w-5 rounded-full"
          width={20}
          height={20}
        />
        {showLabel && <span>Chat with Jack</span>}
        {!showLabel && <MessageCircle className="h-4 w-4" />}
      </Button>

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Image
                src={jackAvatar}
                alt="Jack"
                className="h-6 w-6 rounded-full"
                width={24}
                height={24}
              />
              Chat with Jack
            </DialogTitle>
            <DialogDescription>
              Get help with your project from our AI assistant.
            </DialogDescription>
          </DialogHeader>
          <div className="h-96">
            <PopupChat
              title="Chat with Jack"
              supportName="Jack"
              supportAvatar={jackAvatar.src}
              userName={user?.name || "there"}
              userAvatar={user?.image}
              userRole={user?.role || "homeowner"}
              className="h-full"
            />
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}