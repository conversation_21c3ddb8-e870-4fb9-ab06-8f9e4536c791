"use client";

import { Card, CardContent } from "@/components/ui/card";

interface MobileQuickStatsProps {
  stats: Array<{
    label: string;
    value: string | number;
    icon?: React.ReactNode;
    color?: string;
  }>;
}

export function MobileQuickStats({ stats }: MobileQuickStatsProps) {
  return (
    <div className="grid grid-cols-2 gap-4">
      {stats.map((stat, index) => (
        <Card key={index} className="overflow-hidden">
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              {stat.icon && (
                <div
                  className={`rounded-full p-2 ${
                    stat.color || "bg-primary/10 text-primary"
                  }`}
                >
                  {stat.icon}
                </div>
              )}
              <div>
                <p className="text-sm font-medium">{stat.label}</p>
                <p className="text-2xl font-bold">{stat.value}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}