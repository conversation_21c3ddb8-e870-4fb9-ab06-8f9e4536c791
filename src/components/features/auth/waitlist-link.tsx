"use client";

import Link from "next/link";
import { useWaitlistModal } from "@/hooks/use-waitlist-modal";

interface WaitlistLinkProps {
  href: string;
  children: React.ReactNode;
  className?: string;
  accountType?: "homeowner" | "contractor";
  [key: string]: any; // For any other props that might be passed
}

export function WaitlistLink({
  href,
  children,
  className,
  accountType,
  ...props
}: WaitlistLinkProps) {
  const { handleSignUpClick, isWaitlistEnabled } = useWaitlistModal();

  // Determine account type from href if not explicitly provided
  const determineAccountType = () => {
    if (accountType) return accountType;
    if (href.includes("/homeowner")) return "homeowner";
    if (href.includes("/contractor")) return "contractor";
    return undefined;
  };

  const type = determineAccountType();

  if (isWaitlistEnabled && href.includes("/sign-up")) {
    return (
      <Link
        href={href}
        className={className}
        onClick={(e) => handleSignUpClick(e, type, href)}
        {...props}
      >
        {children}
      </Link>
    );
  }

  // If waitlist is not enabled, render a normal link
  return (
    <Link href={href} className={className} {...props}>
      {children}
    </Link>
  );
}