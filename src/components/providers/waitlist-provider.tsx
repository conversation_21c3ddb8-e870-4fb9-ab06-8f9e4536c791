"use client";

import { usePathname } from "next/navigation";
import { useFeatureFlagEnabled } from "posthog-js/react";
import {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useState,
} from "react";
import { WaitlistModal } from "@/components/features/auth/waitlist-modal";
import { usePostHogAnonymous } from "@/hooks/use-posthog-anonymous";

// Feature flag name
const WAITLIST_FEATURE_FLAG = "enable-waitlist";

interface WaitlistContextType {
  showWaitlistModal: (accountType?: "homeowner" | "contractor") => void;
  isWaitlistEnabled: boolean;
}

const WaitlistContext = createContext<WaitlistContextType | undefined>(
  undefined,
);

export function WaitlistProvider({ children }: { children: React.ReactNode }) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [accountType, setAccountType] = useState<
    "homeowner" | "contractor" | undefined
  >(undefined);
  const pathname = usePathname();

  // Use both hooks to ensure feature flag works for anonymous users
  const standardWaitlistEnabled = useFeatureFlagEnabled(WAITLIST_FEATURE_FLAG);
  const { captureAnonymous, isAnonymousTrackingReady, isFeatureEnabled } =
    usePostHogAnonymous();

  // Get feature flag value using anonymous utilities (more reliable for anonymous users)
  const anonymousWaitlistEnabled = isFeatureEnabled(WAITLIST_FEATURE_FLAG);

  // Use either flag evaluation method - prefer anonymous method for anonymous users
  const waitlistEnabled = standardWaitlistEnabled || anonymousWaitlistEnabled;

  // Debug logging in development
  useEffect(() => {
    if (process.env.NODE_ENV === "development") {
      console.log("Waitlist Debug:", {
        standardWaitlistEnabled,
        anonymousWaitlistEnabled,
        finalWaitlistEnabled: waitlistEnabled,
        isAnonymousTrackingReady,
        flagName: WAITLIST_FEATURE_FLAG,
      });
    }
  }, [
    standardWaitlistEnabled,
    anonymousWaitlistEnabled,
    waitlistEnabled,
    isAnonymousTrackingReady,
  ]);

  // Function to show the waitlist modal
  const showWaitlistModal = useCallback(
    (type?: "homeowner" | "contractor") => {
      if (waitlistEnabled) {
        setAccountType(type);
        setIsModalOpen(true);

        // Track the waitlist modal view with anonymous tracking
        if (isAnonymousTrackingReady) {
          captureAnonymous("waitlist_modal_shown", {
            account_type: type || "unknown",
            path: pathname,
            signup_source: "modal_trigger",
          });
        }
      }
    },
    [waitlistEnabled, pathname, captureAnonymous, isAnonymousTrackingReady],
  );

  // Handle sign-up link clicks
  useEffect(() => {
    if (!waitlistEnabled) {
      if (process.env.NODE_ENV === "development") {
        console.log("Waitlist link handler not active - waitlist disabled");
      }
      return;
    }

    // Function to intercept clicks on sign-up links
    const handleLinkClick = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      const link = target.closest("a");

      if (!link) return;

      const href = link.getAttribute("href");
      if (!href) return;

      // Debug logging
      if (process.env.NODE_ENV === "development") {
        console.log("Link clicked:", {
          href,
          isSignUpLink: href.includes("/sign-up"),
        });
      }

      // Check if this is a sign-up link
      if (href.includes("/sign-up")) {
        e.preventDefault();

        // Determine account type from the URL
        let type: "homeowner" | "contractor" | undefined;
        if (href.includes("/homeowner")) {
          type = "homeowner";
        } else if (href.includes("/contractor")) {
          type = "contractor";
        }

        if (process.env.NODE_ENV === "development") {
          console.log("Showing waitlist modal for type:", type);
        }

        showWaitlistModal(type);
      }
    };

    // Add event listener
    document.addEventListener("click", handleLinkClick);

    // Clean up
    return () => {
      document.removeEventListener("click", handleLinkClick);
    };
  }, [waitlistEnabled, showWaitlistModal]);

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  const handleSubmitSuccess = () => {
    // Track successful submission with anonymous tracking
    if (isAnonymousTrackingReady) {
      captureAnonymous("waitlist_submission_success", {
        account_type: accountType || "unknown",
        signup_source: "waitlist_modal",
      });
    }
  };

  const isWaitlistEnabled = !!waitlistEnabled;

  return (
    <WaitlistContext.Provider value={{ showWaitlistModal, isWaitlistEnabled }}>
      {children}
      <WaitlistModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        accountType={accountType}
        onSubmitSuccess={handleSubmitSuccess}
      />
    </WaitlistContext.Provider>
  );
}

export function useWaitlist() {
  const context = useContext(WaitlistContext);
  if (context === undefined) {
    throw new Error("useWaitlist must be used within a WaitlistProvider");
  }
  return context;
}
