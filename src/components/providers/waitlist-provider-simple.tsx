"use client";

import { usePathname } from "next/navigation";
import {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useState,
} from "react";
import { WaitlistModal } from "@/components/features/auth/waitlist-modal";
import { usePostHogAnonymous } from "@/hooks/use-posthog-anonymous";

/**
 * Simplified waitlist provider that doesn't rely on feature flags
 * Use this for testing if the main provider has feature flag issues
 */

interface WaitlistContextType {
  showWaitlistModal: (accountType?: "homeowner" | "contractor") => void;
  isWaitlistEnabled: boolean;
}

const WaitlistContext = createContext<WaitlistContextType | undefined>(
  undefined,
);

interface WaitlistProviderSimpleProps {
  children: React.ReactNode;
  enabled?: boolean; // Manual control instead of feature flag
}

export function WaitlistProviderSimple({ 
  children, 
  enabled = true 
}: WaitlistProviderSimpleProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [accountType, setAccountType] = useState<
    "homeowner" | "contractor" | undefined
  >(undefined);
  const pathname = usePathname();
  const { captureAnonymous, isAnonymousTrackingReady } = usePostHogAnonymous();

  // Function to show the waitlist modal
  const showWaitlistModal = useCallback(
    (type?: "homeowner" | "contractor") => {
      console.log("showWaitlistModal called with type:", type, "enabled:", enabled);
      
      if (enabled) {
        setAccountType(type);
        setIsModalOpen(true);

        // Track the waitlist modal view with anonymous tracking
        if (isAnonymousTrackingReady) {
          captureAnonymous("waitlist_modal_shown", {
            account_type: type || "unknown",
            path: pathname,
            signup_source: "modal_trigger",
          });
        }
      } else {
        console.log("Waitlist is disabled");
      }
    },
    [enabled, pathname, captureAnonymous, isAnonymousTrackingReady],
  );

  // Handle sign-up link clicks
  useEffect(() => {
    if (!enabled) {
      console.log("Waitlist link handler not active - waitlist disabled");
      return;
    }

    console.log("Setting up waitlist link handler");

    // Function to intercept clicks on sign-up links
    const handleLinkClick = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      const link = target.closest("a");

      if (!link) return;

      const href = link.getAttribute("href");
      if (!href) return;

      console.log("Link clicked:", { href, isSignUpLink: href.includes("/sign-up") });

      // Check if this is a sign-up link
      if (href.includes("/sign-up")) {
        e.preventDefault();

        // Determine account type from the URL
        let type: "homeowner" | "contractor" | undefined;
        if (href.includes("/homeowner")) {
          type = "homeowner";
        } else if (href.includes("/contractor")) {
          type = "contractor";
        }

        console.log("Showing waitlist modal for type:", type);
        showWaitlistModal(type);
      }
    };

    // Add event listener
    document.addEventListener("click", handleLinkClick);
    console.log("Waitlist link handler attached");

    // Clean up
    return () => {
      document.removeEventListener("click", handleLinkClick);
      console.log("Waitlist link handler removed");
    };
  }, [enabled, showWaitlistModal]);

  const handleCloseModal = () => {
    console.log("Closing waitlist modal");
    setIsModalOpen(false);
  };

  const handleSubmitSuccess = () => {
    console.log("Waitlist submission success");
    // Track successful submission with anonymous tracking
    if (isAnonymousTrackingReady) {
      captureAnonymous("waitlist_submission_success", {
        account_type: accountType || "unknown",
        signup_source: "waitlist_modal",
      });
    }
  };

  console.log("WaitlistProviderSimple render:", { 
    enabled, 
    isModalOpen, 
    accountType,
    isAnonymousTrackingReady 
  });

  return (
    <WaitlistContext.Provider value={{ showWaitlistModal, isWaitlistEnabled: enabled }}>
      {children}
      <WaitlistModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        accountType={accountType}
        onSubmitSuccess={handleSubmitSuccess}
      />
    </WaitlistContext.Provider>
  );
}

export function useWaitlistSimple() {
  const context = useContext(WaitlistContext);
  if (context === undefined) {
    throw new Error("useWaitlistSimple must be used within a WaitlistProviderSimple");
  }
  return context;
}

/**
 * Test component for the simple waitlist provider
 */
export function WaitlistTestSimple() {
  const { showWaitlistModal, isWaitlistEnabled } = useWaitlistSimple();

  if (process.env.NODE_ENV === "production") {
    return null;
  }

  return (
    <div className="fixed top-4 left-4 bg-background/90 backdrop-blur p-4 rounded border space-y-2">
      <div className="text-sm font-medium">Simple Waitlist Test</div>
      <div className="text-xs">Enabled: {isWaitlistEnabled ? "Yes" : "No"}</div>
      
      <div className="space-y-1">
        <button 
          onClick={() => showWaitlistModal("homeowner")}
          className="block w-full text-left text-xs bg-blue-100 hover:bg-blue-200 px-2 py-1 rounded"
        >
          Test Homeowner
        </button>
        <button 
          onClick={() => showWaitlistModal("contractor")}
          className="block w-full text-left text-xs bg-green-100 hover:bg-green-200 px-2 py-1 rounded"
        >
          Test Contractor
        </button>
      </div>

      <div className="space-y-1 text-xs">
        <div>Test Links:</div>
        <a href="/sign-up/homeowner" className="block text-blue-600 hover:underline">
          /sign-up/homeowner
        </a>
        <a href="/sign-up/contractor" className="block text-blue-600 hover:underline">
          /sign-up/contractor
        </a>
      </div>
    </div>
  );
}
