import { ArrowRightIcon, MessageCircle, Sparkles } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useId } from "react";
import jackAvatar from "@/assets/images/jack.png";
import { Avatar } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { buttonVariants } from "@/components/ui/button";

const demoConversation = [
  {
    role: "user",
    content: "I want to renovate my kitchen but I'm not sure where to start.",
  },
  {
    role: "assistant",
    content:
      "I'd be happy to help you get started with your kitchen renovation! Here's what we should consider first:\n\n1. **Budget planning**: What's your approximate budget for this project?\n\n2. **Scope of work**: Are you looking for a complete renovation (cabinets, countertops, appliances, flooring) or focusing on specific elements?\n\n3. **Timeline**: When would you like to start and how quickly do you need it completed?\n\nOnce we have these basics, I can help you create a detailed plan, find qualified contractors, and guide you through the entire process!",
  },
  {
    role: "user",
    content:
      "My budget is around $15,000 and I mainly want new countertops and cabinets.",
  },
  {
    role: "assistant",
    content:
      "Great! With a $15,000 budget focused on countertops and cabinets, here's my recommendation:\n\n**Countertops (~$3,000-5,000)**\n- Quartz or granite would be excellent durable options within your budget\n- For your budget, you could cover approximately 30-40 sq ft of countertop space\n\n**Cabinets (~$8,000-10,000)**\n- Semi-custom cabinets would give you good quality and some personalization options\n- Consider refinishing existing cabinet boxes and replacing doors to save money\n\n**Next steps:**\n1. I can help you find 3-4 qualified contractors who specialize in kitchen renovations\n2. Would you like to see some design inspirations that would work with your budget?\n3. Let's schedule a measurement appointment to get accurate quotes",
  },
];

export function MeetJackDemo() {
  const id = useId();

  return (
    <div className="relative bg-gray-50 py-24 sm:py-32">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <div className="mx-auto max-w-2xl lg:text-center">
          <h2 className="font-semibold text-base text-orange-600 leading-7">
            See Jack in Action
          </h2>
          <p className="mt-2 font-bold text-3xl text-gray-900 tracking-tight sm:text-4xl">
            Your Personal Home Project Expert
          </p>
          <p className="mt-6 text-gray-600 text-lg leading-8">
            Jack provides expert guidance for all your home improvement needs.
            From planning and budgeting to finding the right contractors, Jack
            makes the entire process smoother and more successful.
          </p>
        </div>

        <div className="mx-auto mt-16 max-w-3xl rounded-xl border-2 border-tradecrews-blue/20 bg-gradient-to-b from-white to-tradecrews-blue-50/20 shadow-xl">
          {/* Enhanced header matching chat style */}
          <div className="rounded-t-xl border-b bg-gradient-to-r from-tradecrews-blue-50 to-tradecrews-orange-50 p-4">
            <div className="flex items-center gap-3">
              <div className="relative">
                <Avatar className="h-10 w-10 border border-tradecrews-blue/20">
                  <Image
                    src={jackAvatar}
                    alt="Jack AI Assistant"
                    width={40}
                    height={40}
                    className="object-cover"
                  />
                </Avatar>
                <div className="-right-1 -top-1 absolute flex h-4 w-4 items-center justify-center rounded-full bg-tradecrews-orange">
                  <Sparkles className="h-2 w-2 text-white" />
                </div>
              </div>
              <div className="flex-1">
                <div className="flex items-center gap-2">
                  <h3 className="font-semibold text-gray-900">Jack</h3>
                  <Badge
                    variant="outline"
                    className="border-tradecrews-orange/20 bg-tradecrews-orange/10 text-tradecrews-orange text-xs"
                  >
                    AI Assistant
                  </Badge>
                </div>
                <p className="text-gray-600 text-xs">Home Project Expert</p>
              </div>
              <div className="flex items-center gap-1">
                <div className="h-2 w-2 rounded-full bg-green-500" />
                <span className="text-gray-600 text-xs">Online</span>
              </div>
            </div>
          </div>

          <div className="p-6">
            <div className="space-y-4">
              {demoConversation.map((message, index) => (
                <div
                  key={`message-${index}-${id}`}
                  className={`group flex ${
                    message.role === "user" ? "justify-end" : "justify-start"
                  }`}
                >
                  {message.role !== "user" && (
                    <div className="mr-3 flex-shrink-0 self-start">
                      <Avatar className="h-8 w-8 border border-tradecrews-blue/20">
                        <Image
                          src={jackAvatar}
                          alt="Jack AI Assistant"
                          width={32}
                          height={32}
                          className="object-cover"
                        />
                      </Avatar>
                    </div>
                  )}

                  {/* Enhanced message bubble matching chat style */}
                  <div
                    className={`relative max-w-[80%] rounded-lg p-3 transition-all ${
                      message.role === "user"
                        ? "bg-tradecrews-blue text-white"
                        : "border border-gray-200 bg-white shadow-sm"
                    }`}
                  >
                    {/* Message header for Jack's messages */}
                    {message.role !== "user" && (
                      <div className="mb-1 flex items-center gap-2">
                        <span className="font-medium text-gray-900 text-xs">
                          Jack
                        </span>
                        <Badge
                          variant="outline"
                          className="h-4 border-tradecrews-orange/20 bg-tradecrews-orange/5 px-1 py-0 text-[10px] text-tradecrews-orange"
                        >
                          AI Assistant
                        </Badge>
                      </div>
                    )}

                    {/* Message content */}
                    <p
                      className={`whitespace-pre-wrap text-sm ${
                        message.role === "user" ? "text-white" : "text-gray-700"
                      }`}
                    >
                      {message.content}
                    </p>
                  </div>

                  {message.role === "user" && (
                    <div className="ml-3 flex-shrink-0 self-center">
                      <Avatar className="flex h-8 w-8 items-center justify-center bg-tradecrews-blue/80 text-white">
                        <span className="font-medium text-xs">You</span>
                      </Avatar>
                    </div>
                  )}
                </div>
              ))}
            </div>

            {/* Enhanced input area matching chat style */}
            <div className="mt-6 border-gray-200 border-t p-4">
              <div className="flex items-center gap-3 rounded-lg border border-gray-200 bg-white p-3 shadow-sm">
                <input
                  type="text"
                  placeholder="Ask Jack about your home project..."
                  className="flex-1 border-none bg-transparent text-sm outline-none placeholder:text-gray-400"
                  disabled
                />
                <button
                  type="button"
                  className="flex h-8 w-8 items-center justify-center rounded-full bg-gradient-to-r from-tradecrews-blue to-tradecrews-blue-600 text-white transition-colors hover:from-tradecrews-blue-600 hover:to-tradecrews-blue-700 disabled:opacity-50"
                  disabled
                >
                  <MessageCircle className="h-4 w-4" />
                </button>
              </div>
            </div>
          </div>
        </div>

        <div className="mx-auto mt-12 flex max-w-md flex-col items-center justify-center gap-4 text-center">
          <p className="text-gray-600">
            Ready to experience Jack's assistance with your home projects?
          </p>
          <Link
            href="/sign-up/homeowner"
            className={buttonVariants({
              variant: "tc_orange",
              size: "lg",
              className: "gap-2",
            })}
          >
            Try Jack Now <ArrowRightIcon className="h-4 w-4" />
          </Link>
        </div>
      </div>
    </div>
  );
}
