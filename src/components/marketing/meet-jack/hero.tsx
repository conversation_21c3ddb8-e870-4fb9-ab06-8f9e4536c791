import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>rk<PERSON> } from "lucide-react";
import Link from "next/link";
import { StaticJackIntroduction } from "@/components/marketing/meet-jack/static-jack-introduction";
import { Card, CardContent } from "@/components/ui/card";

export function MeetJackHero() {
  return (
    <div className="relative isolate overflow-hidden bg-white">
      {/* Background decoration */}
      <div className="-z-10 sm:-top-80 -top-40 absolute inset-x-0 transform-gpu overflow-hidden blur-3xl">
        <div
          className="-translate-x-1/2 relative left-[calc(50%-11rem)] aspect-[1155/678] w-[36.125rem] rotate-[30deg] bg-gradient-to-tr from-tradecrews-blue-500 to-tradecrews-orange-500 opacity-20 sm:left-[calc(50%-30rem)] sm:w-[72.1875rem]"
          style={{
            clipPath:
              "polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)",
          }}
        />
      </div>

      <div className="mx-auto max-w-7xl px-6 py-24 sm:py-32 lg:flex lg:items-center lg:gap-x-10 lg:px-8 lg:py-40">
        <div className="mx-auto max-w-2xl lg:mx-0 lg:flex-auto">
          <div className="flex">
            <div className="relative flex h-12 w-12 items-center justify-center rounded-full bg-tradecrews-blue">
              <Bot className="h-6 w-6 text-white" />
              <div className="-top-1 -right-1 absolute flex h-5 w-5 items-center justify-center rounded-full bg-tradecrews-orange">
                <Sparkles className="h-3 w-3 text-white" />
              </div>
            </div>
          </div>
          <h1 className="mt-10 max-w-lg font-bold text-4xl text-gray-900 tracking-tight sm:text-6xl">
            Meet Jack, Your AI Home Project Assistant
          </h1>
          <p className="mt-6 text-gray-600 text-lg leading-8">
            Jack is your personal AI assistant for home improvement projects.
            Get expert guidance on planning, budgeting, finding contractors, and
            more—all in one intelligent, easy-to-use interface.
          </p>
          <div className="mt-10 flex items-center gap-x-6">
            <Link
              href="/sign-up/homeowner"
              className="rounded-md bg-tradecrews-orange px-3.5 py-2.5 font-semibold text-white shadow-sm hover:bg-tradecrews-orange-600 focus-visible:outline-2 focus-visible:outline-tradecrews-orange focus-visible:outline-offset-2"
            >
              Try Jack Now
            </Link>
            <Link
              href="#demo"
              className="flex items-center gap-1 font-semibold text-gray-900 leading-6"
            >
              See Jack in action <ArrowRight className="h-4 w-4" />
            </Link>
          </div>
        </div>
        <div className="mt-16 sm:mt-24 lg:mt-0 lg:flex-shrink-0 lg:flex-grow">
          <Card className="mx-auto w-full max-w-[450px] overflow-hidden rounded-xl border-2 border-tradecrews-blue/20 bg-gradient-to-br from-tradecrews-blue-50 via-white to-tradecrews-orange-50 shadow-xl">
            <CardContent className="p-6">
              {/* Static Jack Introduction */}
              <StaticJackIntroduction userName="Guest" userRole="homeowner" />
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
