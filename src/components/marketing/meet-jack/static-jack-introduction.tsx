import { <PERSON><PERSON><PERSON>, <PERSON>rk<PERSON> } from "lucide-react";
import Image from "next/image";
import jackAvatar from "@/assets/images/jack.png";
import { Avatar } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";

interface StaticJackIntroductionProps {
  userName: string;
  userRole: string;
}

export function StaticJackIntroduction({
  userName,
  userRole,
}: StaticJackIntroductionProps) {
  const getQuickActions = () => {
    switch (userRole) {
      case "homeowner":
        return [
          { label: "Create a new project", action: "create-project" },
          { label: "Find contractors", action: "find-contractors" },
          { label: "Get project timeline", action: "project-timeline" },
        ];
      case "contractor":
        return [
          { label: "Find relevant jobs", action: "find-jobs" },
          { label: "Analyze competition", action: "analyze-competition" },
          { label: "Check performance", action: "check-performance" },
        ];
      case "admin":
        return [
          { label: "View platform insights", action: "platform-insights" },
          { label: "Check user stats", action: "user-stats" },
          { label: "Manage users", action: "manage-users" },
        ];
      default:
        return [
          { label: "Explore features", action: "explore-features" },
          { label: "Get help", action: "get-help" },
        ];
    }
  };

  const quickActions = getQuickActions();

  return (
    <div className="space-y-4">
      {/* Jack's Introduction Message */}
      <div className="flex items-start gap-4">
        <div className="relative">
          <Avatar className="h-12 w-12 border border-tradecrews-blue/20">
            <Image
              src={jackAvatar}
              alt="Jack AI Assistant"
              width={48}
              height={48}
              className="object-cover"
            />
          </Avatar>
          <div className="-top-1 -right-1 absolute flex h-5 w-5 items-center justify-center rounded-full bg-tradecrews-orange">
            <Sparkles className="h-3 w-3 text-white" />
          </div>
        </div>
        <div className="flex-1">
          <div className="mb-2 flex items-center gap-2">
            <h3 className="font-bold text-gray-900 text-lg">Hi {userName}!</h3>
            <Badge
              variant="secondary"
              className="bg-tradecrews-orange-100 text-tradecrews-orange-700"
            >
              AI Assistant
            </Badge>
          </div>
          <p className="text-gray-600 leading-relaxed">
            I'm <strong className="text-gray-900">Jack</strong>, your AI
            assistant here at TradeCrews! I'm excited to help you
            {userRole === "homeowner" &&
              " manage your home improvement projects and connect with the best contractors."}
            {userRole === "contractor" &&
              " find great projects, optimize your bids, and grow your business."}
            {userRole === "admin" &&
              " manage the platform, analyze user data, and maintain system health."}
          </p>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="space-y-3">
        <h4 className="flex items-center gap-2 font-semibold text-gray-900 text-sm">
          <ArrowRight className="h-4 w-4 text-tradecrews-orange" />
          Here's how I can help you today:
        </h4>
        <div className="grid gap-2">
          {quickActions.map((action, index) => (
            <div
              key={action.action}
              className="flex items-center justify-start gap-2 rounded-md border border-tradecrews-blue/30 bg-white p-2 text-left font-medium text-gray-800 text-sm shadow-sm hover:border-tradecrews-blue hover:bg-tradecrews-blue-50"
            >
              <span className="flex h-6 w-6 items-center justify-center rounded-full bg-tradecrews-blue-600 font-medium text-white text-xs">
                {index + 1}
              </span>
              {action.label}
            </div>
          ))}
        </div>
      </div>

      {/* Call to Action */}
      <div className="rounded-lg border border-tradecrews-orange/30 bg-tradecrews-orange-50 p-4 shadow-sm">
        <p className="text-center text-gray-700 text-sm">
          <strong className="text-tradecrews-orange-700">
            Just ask me anything!
          </strong>{" "}
          I can help with projects, contractors, scheduling, and much more.
        </p>
      </div>
    </div>
  );
}
