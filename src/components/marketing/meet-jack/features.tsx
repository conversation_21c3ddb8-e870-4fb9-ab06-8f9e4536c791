import {
  Bo<PERSON>,
  <PERSON>,
  ClipboardList,
  CreditCard,
  Lightbulb,
  MessageSquareText,
  Sparkles,
  Users,
} from "lucide-react";

const features = [
  {
    name: "Personalized Project Planning",
    description:
      "Jack helps you plan your home improvement projects with personalized recommendations based on your specific needs, budget, and timeline.",
    icon: ClipboardList,
  },
  {
    name: "Contractor Matching",
    description:
      "Get matched with the perfect contractors for your project. Jack analyzes your requirements and suggests professionals with the right skills and experience.",
    icon: Users,
  },
  {
    name: "Budget Optimization",
    description:
      "Receive smart budget recommendations and cost-saving tips tailored to your project. Jack helps you get the best value without compromising on quality.",
    icon: CreditCard,
  },
  {
    name: "Project Timeline Assistance",
    description:
      "Jack helps you create realistic project timelines, anticipate potential delays, and keep your renovation on schedule from start to finish.",
    icon: Calendar,
  },
  {
    name: "Instant Answers",
    description:
      "Get immediate answers to your home improvement questions, from material selection to building codes and best practices for your specific project.",
    icon: MessageSquareText,
  },
  {
    name: "Smart Suggestions",
    description:
      "Receive proactive suggestions and ideas to improve your project based on the latest trends, materials, and techniques in home improvement.",
    icon: Lightbulb,
  },
];

export function MeetJackFeatures() {
  return (
    <div className="relative bg-white py-24 sm:py-32">
      {/* Background decoration */}
      <div className="-top-40 -z-10 sm:-top-80 absolute inset-x-0 transform-gpu overflow-hidden blur-3xl">
        <div
          className="-translate-x-1/2 relative left-[calc(50%-11rem)] aspect-[1155/678] w-[36.125rem] rotate-[30deg] bg-gradient-to-tr from-orange-400 to-orange-600 opacity-10 sm:left-[calc(50%-30rem)] sm:w-[72.1875rem]"
          style={{
            clipPath:
              "polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)",
          }}
        />
      </div>

      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <div className="mx-auto max-w-2xl lg:text-center">
          <div className="flex justify-center">
            <div className="relative">
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-tradecrews-blue text-white">
                <Bot className="h-6 w-6" />
              </div>
              <div className="-top-1 -right-1 absolute flex h-5 w-5 items-center justify-center rounded-full bg-tradecrews-orange">
                <Sparkles className="h-3 w-3 text-white" />
              </div>
            </div>
          </div>
          <h2 className="mt-4 font-semibold text-base text-orange-600 leading-7">
            AI-Powered Assistance
          </h2>
          <p className="mt-2 font-bold text-3xl text-gray-900 tracking-tight sm:text-4xl">
            How Jack Makes Home Projects Easier
          </p>
          <p className="mt-6 text-gray-600 text-lg leading-8">
            Jack combines artificial intelligence with deep knowledge of home
            improvement to provide personalized assistance throughout your
            project journey. From planning to completion, Jack is your trusted
            advisor every step of the way.
          </p>
        </div>

        <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
          <div className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-3">
            {features.map((feature) => (
              <div
                key={feature.name}
                className="flex flex-col rounded-xl border border-gray-200 bg-gradient-to-b from-white to-orange-50/30 p-6 shadow-sm transition-all hover:border-orange-200 hover:shadow-md hover:from-white hover:to-orange-50/50"
              >
                <div className="mb-5 flex h-12 w-12 items-center justify-center rounded-lg bg-gradient-to-br from-orange-50 to-orange-100 ring-1 ring-orange-100/50 shadow-sm">
                  <feature.icon
                    className="h-6 w-6 text-orange-600"
                    aria-hidden="true"
                  />
                </div>
                <h3 className="font-semibold text-gray-900 text-lg leading-7">
                  {feature.name}
                </h3>
                <p className="mt-2 flex-auto text-base text-gray-600 leading-7">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
