import { SparklesIcon } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import jackAvatar from "@/assets/images/jack.png";

export default function JackPromo() {
  return (
    <div className="relative bg-gradient-to-br from-tradecrews-blue-50 via-white to-tradecrews-orange-50 py-16 sm:py-24">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <div className="mx-auto flex max-w-2xl flex-col items-center lg:mx-0 lg:max-w-none lg:flex-row lg:items-center lg:justify-between">
          <div className="lg:max-w-xl">
            <div className="flex">
              <div className="relative flex items-center gap-x-2 rounded-full px-4 py-1 text-gray-600 text-sm leading-6 ring-1 ring-gray-900/10 hover:ring-gray-900/20">
                <span className="font-semibold text-orange-600">New</span>
                <span className="h-0.5 w-0.5 rounded-full bg-gray-500" />
                <span>AI-powered assistance</span>
              </div>
            </div>
            <h2 className="mt-6 font-bold text-3xl text-gray-900 tracking-tight sm:text-4xl">
              Meet Jack, Your Home Project Assistant
            </h2>
            <p className="mt-4 text-gray-600 text-lg leading-8">
              Jack is your AI-powered assistant that makes home improvement
              projects simpler and more successful. Get instant help with
              project planning, contractor selection, and more.
            </p>
            <div className="mt-6 flex gap-4">
              <Link
                href="/meet-jack"
                className="rounded-md bg-tradecrews-blue px-4 py-2.5 font-semibold text-white shadow-sm hover:bg-tradecrews-blue-600 focus-visible:outline-2 focus-visible:outline-tradecrews-blue focus-visible:outline-offset-2"
              >
                Learn more about Jack
              </Link>
            </div>
          </div>
          <div className="mt-10 w-full max-w-md lg:mt-0">
            <div className="relative overflow-hidden rounded-xl border border-gray-200 bg-gradient-to-b from-white to-tradecrews-blue-50/30 p-6 shadow-md">
              <div className="flex items-start gap-4">
                <div className="relative">
                  <div className="flex h-12 w-12 items-center justify-center overflow-hidden rounded-full border border-tradecrews-blue/20 bg-white">
                    <Image
                      src={jackAvatar}
                      alt="Jack AI Assistant"
                      width={48}
                      height={48}
                      className="object-cover"
                    />
                  </div>
                  <div className="-top-1 -right-1 absolute flex h-5 w-5 items-center justify-center rounded-full bg-orange-500">
                    <SparklesIcon className="size-3 text-white" />
                  </div>
                </div>
                <div className="flex-1">
                  <h3 className="mb-2 font-bold text-gray-900 text-lg">
                    Hi there!
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    I'm <strong className="text-gray-900">Jack</strong>, your AI
                    assistant here at TradeCrews! I can help you with your home
                    improvement projects.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
