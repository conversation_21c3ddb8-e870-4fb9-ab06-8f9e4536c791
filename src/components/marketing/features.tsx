import {
  CalendarClockIcon,
  ClipboardCheckIcon,
  CreditCardIcon,
  MessageSquareTextIcon,
  ShieldCheckIcon,
  StarIcon,
} from "lucide-react";
import Link from "next/link";

const features = [
  {
    name: "Verified Professionals",
    description:
      "Every trade professional on our platform undergoes thorough verification. We check credentials, licenses, and insurance to ensure you're working with qualified experts you can trust.",
    href: "/verification-process",
    icon: ShieldCheckIcon,
  },
  {
    name: "Competitive Bidding",
    description:
      "Receive multiple quotes from qualified professionals for your project. Compare rates, reviews, and expertise to make informed decisions and find the best value for your budget.",
    href: "/how-bidding-works",
    icon: StarIcon,
  },
  {
    name: "Project Management",
    description:
      "Track your project from start to finish with our intuitive management tools. Schedule appointments, communicate with contractors, and maintain all project documentation in one place.",
    href: "/project-management",
    icon: ClipboardCheckIcon,
  },
  {
    name: "Secure Messaging",
    description:
      "Communicate directly with professionals through our secure messaging system. Discuss project details, share photos, and keep all your conversations organized in one place.",
    href: "/messaging",
    icon: MessageSquareTextIcon,
  },
  {
    name: "Secure Payments",
    description:
      "Pay for services with confidence through our secure payment system. Release funds only when you're satisfied with the work, providing protection for both homeowners and professionals.",
    href: "/payments",
    icon: CreditCardIcon,
  },
  {
    name: "Scheduling",
    description:
      "Easily schedule appointments and track project timelines. Our calendar integration helps you stay on top of your renovation schedule and coordinate with professionals efficiently.",
    href: "/scheduling",
    icon: CalendarClockIcon,
  },
];

export default function Features() {
  return (
    <div className="relative bg-white py-24 sm:py-32">
      {/* Background decoration */}
      <div className="-top-40 -z-10 sm:-top-80 absolute inset-x-0 transform-gpu overflow-hidden blur-3xl">
        <div
          className="-translate-x-1/2 relative left-[calc(50%-11rem)] aspect-[1155/678] w-[36.125rem] rotate-[30deg] bg-gradient-to-tr from-orange-400 to-orange-600 opacity-10 sm:left-[calc(50%-30rem)] sm:w-[72.1875rem]"
          style={{
            clipPath:
              "polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)",
          }}
        />
      </div>

      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <div className="mx-auto max-w-2xl lg:text-center">
          <h2 className="font-semibold text-base text-orange-600 leading-7">
            Find with confidence
          </h2>
          <p className="mt-2 font-bold text-3xl text-gray-900 tracking-tight sm:text-4xl">
            Everything you need to hire the right professional
          </p>
          <p className="mt-6 text-gray-600 text-lg leading-8">
            Our platform simplifies finding and hiring qualified trade
            professionals. With verified contractors, transparent bidding, and
            comprehensive project management tools, you can focus on what
            matters - getting the job done right.
          </p>
        </div>

        <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
          <div className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-3">
            {features.map((feature) => (
              <div
                key={feature.name}
                className="flex flex-col rounded-xl border border-gray-200 bg-gradient-to-b from-white to-orange-50/30 p-6 shadow-sm transition-all hover:border-orange-200 hover:shadow-md hover:from-white hover:to-orange-50/50"
              >
                <div className="mb-5 flex h-12 w-12 items-center justify-center rounded-lg bg-gradient-to-br from-orange-50 to-orange-100 ring-1 ring-orange-100/50 shadow-sm">
                  <feature.icon
                    className="h-6 w-6 text-orange-600"
                    aria-hidden="true"
                  />
                </div>
                <h3 className="font-semibold text-gray-900 text-lg leading-7">
                  {feature.name}
                </h3>
                <p className="mt-2 flex-auto text-base text-gray-600 leading-7">
                  {feature.description}
                </p>
                <div className="mt-4">
                  <Link
                    href={feature.href}
                    className="inline-flex items-center font-semibold text-orange-600 text-sm leading-6 hover:text-orange-500 group"
                  >
                    Learn more 
                    <span className="ml-1 transition-transform duration-200 group-hover:translate-x-1" aria-hidden="true">→</span>
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}