import { StarIcon } from "lucide-react";

export function HowBiddingWorksContent() {
  return (
    <div className="bg-white py-24 sm:py-32">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <div className="mx-auto max-w-2xl lg:text-center">
          <div className="flex justify-center">
            <div className="mb-5 flex h-12 w-12 items-center justify-center rounded-lg bg-gradient-to-br from-orange-50 to-orange-100 ring-1 ring-orange-100/50 shadow-sm">
              <StarIcon
                className="h-8 w-8 text-orange-600"
                aria-hidden="true"
              />
            </div>
          </div>
          <h1 className="mt-2 font-bold text-4xl text-gray-900 tracking-tight sm:text-5xl">
            Competitive Bidding
          </h1>
          <p className="mt-6 text-gray-600 text-lg leading-8">
            Receive multiple quotes from qualified professionals for your
            project. Compare rates, reviews, and expertise to make informed
            decisions and find the best value for your budget.
          </p>
        </div>

        <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
          <div className="grid gap-8 lg:grid-cols-1">
            <div className="rounded-xl border border-gray-100 bg-gradient-to-b from-white to-orange-50/20 p-8 shadow-sm">
              <h2 className="mb-6 font-semibold text-2xl text-gray-900">
                How Our Bidding Process Works
              </h2>

              <div className="space-y-12">
                <div className="flex gap-6">
                  <div className="flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full bg-orange-100">
                    <span className="font-semibold text-orange-600">1</span>
                  </div>
                  <div>
                    <h3 className="mb-2 font-medium text-gray-900 text-xl">
                      Post Your Project
                    </h3>
                    <p className="text-gray-600">
                      Describe your project in detail, including your
                      requirements, timeline, and budget. The more information
                      you provide, the more accurate the bids you'll receive.
                    </p>
                  </div>
                </div>

                <div className="flex gap-6">
                  <div className="flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full bg-orange-100">
                    <span className="font-semibold text-orange-600">2</span>
                  </div>
                  <div>
                    <h3 className="mb-2 font-medium text-gray-900 text-xl">
                      Receive Multiple Bids
                    </h3>
                    <p className="text-gray-600">
                      Verified professionals in your area will review your
                      project and submit competitive bids. Each bid includes
                      pricing, timeline, and a detailed scope of work.
                    </p>
                  </div>
                </div>

                <div className="flex gap-6">
                  <div className="flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full bg-orange-100">
                    <span className="font-semibold text-orange-600">3</span>
                  </div>
                  <div>
                    <h3 className="mb-2 font-medium text-gray-900 text-xl">
                      Compare and Evaluate
                    </h3>
                    <p className="text-gray-600">
                      Review each bid side by side. Compare pricing, timelines,
                      professional ratings, and past project portfolios to find
                      the best match for your needs.
                    </p>
                  </div>
                </div>

                <div className="flex gap-6">
                  <div className="flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full bg-orange-100">
                    <span className="font-semibold text-orange-600">4</span>
                  </div>
                  <div>
                    <h3 className="mb-2 font-medium text-gray-900 text-xl">
                      Ask Questions
                    </h3>
                    <p className="text-gray-600">
                      Use our secure messaging system to ask professionals
                      questions about their bids. Clarify any details before
                      making your decision.
                    </p>
                  </div>
                </div>

                <div className="flex gap-6">
                  <div className="flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full bg-orange-100">
                    <span className="font-semibold text-orange-600">5</span>
                  </div>
                  <div>
                    <h3 className="mb-2 font-medium text-gray-900 text-xl">
                      Select Your Professional
                    </h3>
                    <p className="text-gray-600">
                      Choose the professional whose bid best meets your needs.
                      You're not obligated to select the lowest bid—consider all
                      factors including experience and reviews.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
