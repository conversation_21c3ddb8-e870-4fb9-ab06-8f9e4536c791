import { ShieldCheckIcon } from "lucide-react";

export function VerificationProcessContent() {
  return (
    <div className="bg-white py-24 sm:py-32">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <div className="mx-auto max-w-2xl lg:text-center">
          <div className="flex justify-center">
            <div className="mb-5 flex h-12 w-12 items-center justify-center rounded-lg bg-gradient-to-br from-orange-50 to-orange-100 ring-1 ring-orange-100/50 shadow-sm">
              <ShieldCheckIcon
                className="h-8 w-8 text-orange-600"
                aria-hidden="true"
              />
            </div>
          </div>
          <h1 className="mt-2 font-bold text-4xl text-gray-900 tracking-tight sm:text-5xl">
            Verified Professionals
          </h1>
          <p className="mt-6 text-gray-600 text-lg leading-8">
            Every trade professional on our platform undergoes thorough
            verification. We check credentials, licenses, and insurance to
            ensure you're working with qualified experts you can trust.
          </p>
        </div>

        <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
          <div className="grid gap-x-8 gap-y-16 lg:grid-cols-2">
            <div className="rounded-xl border border-gray-100 bg-gradient-to-b from-white to-orange-50/20 p-8 shadow-sm">
              <h2 className="mb-4 font-semibold text-2xl text-gray-900">
                Our Verification Steps
              </h2>
              <ul className="space-y-4">
                <li className="flex gap-3">
                  <div className="flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full bg-orange-100">
                    <span className="font-medium text-orange-600 text-sm">
                      1
                    </span>
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">
                      Identity Verification
                    </p>
                    <p className="text-gray-600">
                      We confirm the identity of each professional through
                      government-issued ID.
                    </p>
                  </div>
                </li>
                <li className="flex gap-3">
                  <div className="flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full bg-orange-100">
                    <span className="font-medium text-orange-600 text-sm">
                      2
                    </span>
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">
                      License Validation
                    </p>
                    <p className="text-gray-600">
                      We verify all professional licenses with the appropriate
                      regulatory bodies.
                    </p>
                  </div>
                </li>
                <li className="flex gap-3">
                  <div className="flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full bg-orange-100">
                    <span className="font-medium text-orange-600 text-sm">
                      3
                    </span>
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">
                      Insurance Coverage
                    </p>
                    <p className="text-gray-600">
                      We confirm that professionals have appropriate liability
                      insurance coverage.
                    </p>
                  </div>
                </li>
                <li className="flex gap-3">
                  <div className="flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full bg-orange-100">
                    <span className="font-medium text-orange-600 text-sm">
                      4
                    </span>
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">
                      Background Checks
                    </p>
                    <p className="text-gray-600">
                      We conduct background checks to ensure safety and
                      reliability.
                    </p>
                  </div>
                </li>
              </ul>
            </div>

            <div className="rounded-xl border border-gray-100 bg-gradient-to-b from-white to-orange-50/20 p-8 shadow-sm">
              <h2 className="mb-4 font-semibold text-2xl text-gray-900">
                Why Verification Matters
              </h2>
              <p className="mb-6 text-gray-600">
                Working with verified professionals gives you peace of mind and
                protection. Our thorough verification process helps:
              </p>
              <ul className="space-y-4">
                <li className="flex gap-3">
                  <ShieldCheckIcon className="h-6 w-6 flex-shrink-0 text-orange-600" />
                  <p className="text-gray-600">
                    Reduce risks of fraud and substandard work
                  </p>
                </li>
                <li className="flex gap-3">
                  <ShieldCheckIcon className="h-6 w-6 flex-shrink-0 text-orange-600" />
                  <p className="text-gray-600">
                    Ensure you're working with properly insured professionals
                  </p>
                </li>
                <li className="flex gap-3">
                  <ShieldCheckIcon className="h-6 w-6 flex-shrink-0 text-orange-600" />
                  <p className="text-gray-600">
                    Confirm professionals have the qualifications they claim
                  </p>
                </li>
                <li className="flex gap-3">
                  <ShieldCheckIcon className="h-6 w-6 flex-shrink-0 text-orange-600" />
                  <p className="text-gray-600">
                    Provide recourse if issues arise during your project
                  </p>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
