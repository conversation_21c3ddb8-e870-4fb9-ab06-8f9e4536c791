import { PutObjectCommand, S3Client } from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import { env } from "@/env";

/**
 * Cloudflare R2 client configuration
 */
const r2Client = new S3Client({
  region: "auto",
  endpoint: `https://${env.CLOUDFLARE_R2_ACCOUNT_ID}.r2.cloudflarestorage.com`,
  credentials: {
    accessKeyId: env.CLOUDFLARE_R2_ACCESS_KEY_ID,
    secretAccessKey: env.CLOUDFLARE_R2_SECRET_ACCESS_KEY,
  },
});

/**
 * Options for generating presigned URLs
 */
export type PresignedUrlOptions = {
  /**
   * The key (path) for the object in the bucket
   */
  key: string;

  /**
   * Content type of the file being uploaded
   */
  contentType?: string;

  /**
   * How long the presigned URL should be valid (in seconds)
   * Default: 1 hour (3600 seconds)
   */
  expiresIn?: number;

  /**
   * Maximum file size allowed (in bytes)
   * Default: 10MB
   */
  maxFileSize?: number;
};

/**
 * Upload options for direct uploads
 */
export type UploadOptions = {
  /**
   * User ID for organizing uploads
   */
  userId?: string;

  /**
   * Type of upload (e.g., "property-image", "job-image")
   */
  type?: string;

  /**
   * File extension or format
   */
  extension?: string;
};

/**
 * Generate a presigned URL for uploading to Cloudflare R2
 */
export async function generatePresignedUrl({
  key,
  contentType = "image/jpeg",
  expiresIn = 3600, // 1 hour
  maxFileSize = 10 * 1024 * 1024, // 10MB
}: PresignedUrlOptions): Promise<string> {
  const command = new PutObjectCommand({
    Bucket: env.CLOUDFLARE_R2_BUCKET_NAME,
    Key: key,
    ContentType: contentType,
    // Add content length restriction
    ...(maxFileSize && {
      Metadata: {
        "max-file-size": maxFileSize.toString(),
      },
    }),
  });

  const presignedUrl = await getSignedUrl(r2Client, command, {
    expiresIn,
  });

  return presignedUrl;
}

/**
 * Generate a unique file key for uploads
 */
export function generateFileKey({
  userId = "anonymous",
  type = "image",
  extension = "jpg",
}: UploadOptions = {}): string {
  const timestamp = Date.now();
  const randomId = Math.random().toString(36).substring(2, 15);

  // Create a structured path: type/userId/timestamp-randomId.extension
  return `${type}/${userId}/${timestamp}-${randomId}.${extension}`;
}

/**
 * Get the public URL for an uploaded file
 */
export function getPublicUrl(key: string): string {
  return `${env.NEXT_PUBLIC_STORAGE_URL}/${key}`;
}

/**
 * Extract file extension from filename or content type
 */
export function getFileExtension(
  filename?: string,
  contentType?: string,
): string {
  if (filename) {
    const ext = filename.split(".").pop()?.toLowerCase();
    if (ext) return ext;
  }

  if (contentType) {
    const typeMap: Record<string, string> = {
      "image/jpeg": "jpg",
      "image/jpg": "jpg",
      "image/png": "png",
      "image/gif": "gif",
      "image/webp": "webp",
      "image/svg+xml": "svg",
    };
    return typeMap[contentType] || "jpg";
  }

  return "jpg";
}

/**
 * Validate file type for uploads
 */
export function isValidFileType(
  contentType: string,
  allowedTypes: string[] = ["image/*"],
): boolean {
  return allowedTypes.some((type) => {
    if (type.endsWith("/*")) {
      const baseType = type.slice(0, -2);
      return contentType.startsWith(baseType);
    }
    return contentType === type;
  });
}

/**
 * Validate file size
 */
export function isValidFileSize(
  size: number,
  maxSize: number = 10 * 1024 * 1024,
): boolean {
  return size <= maxSize;
}

/**
 * Upload configuration for different types
 */
export const uploadConfigs = {
  "property-image": {
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: ["image/jpeg", "image/png", "image/webp"],
    expiresIn: 3600, // 1 hour
  },
  "job-image": {
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: ["image/jpeg", "image/png", "image/webp"],
    expiresIn: 3600, // 1 hour
  },
  "profile-image": {
    maxFileSize: 5 * 1024 * 1024, // 5MB
    allowedTypes: ["image/jpeg", "image/png"],
    expiresIn: 3600, // 1 hour
  },
} as const;

export type UploadConfigType = keyof typeof uploadConfigs;
