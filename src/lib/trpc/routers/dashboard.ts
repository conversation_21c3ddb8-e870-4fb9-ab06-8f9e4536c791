import { and, count, desc, eq, gte, inArray, lte, sql } from "drizzle-orm";
import { z } from "zod";
import { db } from "@/db";
import { bid, chat, job, message, property } from "@/db/schema";
import { protectedProcedure, router } from "@/lib/trpc/procedures";
import { getUserOrganization } from "@/lib/trpc/utils/permissions";
import {
  withQueryCache,
  withQueryPerformanceMonitoring,
} from "@/lib/trpc/utils/query-performance";
import {
  getOptimizedContractorDashboardStats,
  getOptimizedHomeownerDashboardStats,
} from "./dashboard-queries";

export const dashboardRouter = router({
  // Get jobs with approaching bid deadlines
  getJobsWithDeadlines: protectedProcedure
    .input(
      z.object({
        hoursAhead: z.number().default(24),
      }),
    )
    .query(async ({ input, ctx }) => {
      if (!ctx.userId) {
        return [];
      }

      const userOrg = await getUserOrganization(ctx.userId);
      if (!userOrg?.id) {
        return [];
      }

      const now = new Date();
      const deadline = new Date(
        now.getTime() + input.hoursAhead * 60 * 60 * 1000,
      );

      // Get published jobs with deadlines approaching that this org hasn't bid on yet
      const jobsWithDeadlines = await db
        .select({
          job,
          property,
          hasBid: sql<boolean>`EXISTS(
            SELECT 1 FROM ${bid}
            WHERE ${bid.jobId} = ${job.id}
            AND ${bid.organizationId} = ${userOrg.id}
          )`.as("hasBid"),
        })
        .from(job)
        .leftJoin(property, eq(job.propertyId, property.id))
        .where(
          and(
            eq(job.status, "PUBLISHED"),
            gte(job.deadline, now),
            lte(job.deadline, deadline),
          ),
        )
        .orderBy(job.deadline);

      // Filter out jobs this org has already bid on
      return jobsWithDeadlines.filter((item) => !item.hasBid);
    }),

  // Get pending bids for homeowner's jobs
  getPendingBidsForUser: protectedProcedure.query(async ({ ctx }) => {
    if (!ctx.userId) {
      return [];
    }

    // Get user's properties
    const userProperties = await db
      .select({ id: property.id })
      .from(property)
      .where(eq(property.userId, ctx.userId));

    if (userProperties.length === 0) {
      return [];
    }

    const propertyIds = userProperties.map((p) => p.id);

    // Get jobs for these properties with pending bids
    const pendingBids = await db
      .select({
        bid,
        job,
        property,
      })
      .from(bid)
      .innerJoin(job, eq(bid.jobId, job.id))
      .innerJoin(property, eq(job.propertyId, property.id))
      .where(
        and(eq(bid.status, "PROPOSED"), inArray(job.propertyId, propertyIds)),
      )
      .orderBy(desc(bid.createdAt));

    return pendingBids;
  }),

  // Get unread message count for user
  getUnreadMessageCount: protectedProcedure.query(async ({ ctx }) => {
    if (!ctx.userId) {
      return 0;
    }

    // This is a simplified version - in a real app you'd track read status per user
    // For now, we'll count messages from the last 24 hours in chats the user is part of
    const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);

    const userOrg = await getUserOrganization(ctx.userId);
    const isProfessional = !!userOrg;

    if (isProfessional && userOrg?.id) {
      // For contractors: count messages in chats for their bids
      const [result] = await db
        .select({ count: count() })
        .from(message)
        .innerJoin(chat, eq(message.chatId, chat.id))
        .innerJoin(bid, eq(chat.bidId, bid.id))
        .where(
          and(
            eq(bid.organizationId, userOrg.id),
            gte(message.createdAt, oneDayAgo),
            sql`${message.senderId} != ${ctx.userId}`, // Not sent by this user
          ),
        );

      return result?.count || 0;
    }
    // For homeowners: count messages in chats for their jobs
    const userProperties = await db
      .select({ id: property.id })
      .from(property)
      .where(eq(property.userId, ctx.userId));

    if (userProperties.length === 0) {
      return 0;
    }

    const propertyIds = userProperties.map((p) => p.id);

    const [result] = await db
      .select({ count: count() })
      .from(message)
      .innerJoin(chat, eq(message.chatId, chat.id))
      .innerJoin(job, eq(chat.jobId, job.id))
      .where(
        and(
          inArray(job.propertyId, propertyIds),
          gte(message.createdAt, oneDayAgo),
          sql`${message.senderId} != ${ctx.userId}`, // Not sent by this user
        ),
      );

    return result?.count || 0;
  }),

  // OPTIMIZED: Get enhanced stats for contractors using single query
  getContractorDashboardStats: protectedProcedure.query(async ({ ctx }) => {
    if (!ctx.userId) {
      return null;
    }

    const userOrg = await getUserOrganization(ctx.userId);
    if (!userOrg?.id) {
      return null;
    }

    return withQueryPerformanceMonitoring(
      "dashboard.getContractorStats",
      async () => {
        const cacheKey = `contractor-dashboard-stats:${userOrg.id}`;

        return withQueryCache(
          cacheKey,
          () => getOptimizedContractorDashboardStats(db, userOrg.id as string),
          300000, // 5 minutes cache
        );
      },
    );
  }),

  // OPTIMIZED: Get enhanced stats for homeowners using single query
  getHomeownerDashboardStats: protectedProcedure.query(async ({ ctx }) => {
    if (!ctx.userId) {
      return null;
    }

    return withQueryPerformanceMonitoring(
      "dashboard.getHomeownerStats",
      async () => {
        const cacheKey = `homeowner-dashboard-stats:${ctx.userId}`;

        return withQueryCache(
          cacheKey,
          () => getOptimizedHomeownerDashboardStats(db, ctx.userId),
          300000, // 5 minutes cache
        );
      },
    );
  }),
});
