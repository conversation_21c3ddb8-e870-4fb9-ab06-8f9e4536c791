import { and, eq, sql } from "drizzle-orm";
import type { Db } from "@/db";
import {
  bid,
  job,
  organization,
  property,
  review,
} from "@/db/schema";

/**
 * Dashboard-specific optimized queries
 * 
 * These functions provide optimized database queries specifically for dashboard statistics.
 * They replace multiple separate count queries with single optimized queries.
 */

/**
 * OPTIMIZED: Replace dashboard.ts getContractorDashboardStats multiple queries
 *
 * BEFORE: 4+ separate count queries
 * AFTER: Single query with all contractor statistics
 */
export async function getOptimizedContractorDashboardStats(
  db: Db,
  organizationId: string,
) {
  const [stats] = await db
    .select({
      // Bid statistics
      totalBids: sql<number>`count(distinct ${bid.id})`.as("totalBids"),
      acceptedBids:
        sql<number>`count(distinct ${bid.id}) filter (where ${bid.status} = 'ACCEPTED')`.as(
          "acceptedBids",
        ),
      proposedBids:
        sql<number>`count(distinct ${bid.id}) filter (where ${bid.status} = 'PROPOSED')`.as(
          "proposedBids",
        ),

      // Job statistics
      activeJobs:
        sql<number>`count(distinct ${job.id}) filter (where ${job.status} = 'PUBLISHED' and ${bid.status} = 'ACCEPTED')`.as(
          "activeJobs",
        ),
      completedJobs:
        sql<number>`count(distinct ${job.id}) filter (where ${job.status} = 'COMPLETED')`.as(
          "completedJobs",
        ),

      // Financial statistics
      totalBidValue: sql<number>`sum(${bid.amount})`.as("totalBidValue"),
      avgBidAmount: sql<number>`avg(${bid.amount})`.as("avgBidAmount"),
      acceptedBidValue:
        sql<number>`sum(${bid.amount}) filter (where ${bid.status} = 'ACCEPTED')`.as(
          "acceptedBidValue",
        ),

      // Performance metrics
      winRate: sql<number>`
        case
          when count(distinct ${bid.id}) > 0
          then round((count(distinct ${bid.id}) filter (where ${bid.status} = 'ACCEPTED')::decimal / count(distinct ${bid.id})) * 100, 2)
          else 0
        end
      `.as("winRate"),

      avgRating: sql<number>`avg(${review.rating})`.as("avgRating"),
      reviewCount: sql<number>`count(distinct ${review.id})`.as("reviewCount"),
    })
    .from(organization)
    .leftJoin(bid, eq(organization.id, bid.organizationId))
    .leftJoin(job, eq(bid.jobId, job.id))
    .leftJoin(
      review,
      and(eq(review.jobId, job.id), eq(review.reviewType, "CONTRACTOR_REVIEW")),
    )
    .where(eq(organization.id, organizationId))
    .groupBy(organization.id);

  return (
    stats || {
      totalBids: 0,
      acceptedBids: 0,
      proposedBids: 0,
      activeJobs: 0,
      completedJobs: 0,
      totalBidValue: 0,
      avgBidAmount: 0,
      acceptedBidValue: 0,
      winRate: 0,
      avgRating: 0,
      reviewCount: 0,
    }
  );
}

/**
 * OPTIMIZED: Replace dashboard.ts getHomeownerDashboardStats multiple queries
 *
 * BEFORE: 4+ separate count queries
 * AFTER: Single query with all homeowner statistics
 */
export async function getOptimizedHomeownerDashboardStats(
  db: Db,
  userId: string,
) {
  const [stats] = await db
    .select({
      // Property statistics
      totalProperties: sql<number>`count(distinct ${property.id})`.as(
        "totalProperties",
      ),

      // Job statistics
      totalJobs: sql<number>`count(distinct ${job.id})`.as("totalJobs"),
      activeJobs:
        sql<number>`count(distinct ${job.id}) filter (where ${job.status} = 'PUBLISHED')`.as(
          "activeJobs",
        ),
      awardedJobs:
        sql<number>`count(distinct ${job.id}) filter (where ${job.status} = 'AWARDED')`.as(
          "awardedJobs",
        ),
      completedJobs:
        sql<number>`count(distinct ${job.id}) filter (where ${job.status} = 'COMPLETED')`.as(
          "completedJobs",
        ),
      draftJobs:
        sql<number>`count(distinct ${job.id}) filter (where ${job.status} = 'DRAFT')`.as(
          "draftJobs",
        ),

      // Bid statistics
      totalBids: sql<number>`count(distinct ${bid.id})`.as("totalBids"),
      pendingBids:
        sql<number>`count(distinct ${bid.id}) filter (where ${bid.status} = 'PROPOSED')`.as(
          "pendingBids",
        ),
      acceptedBids:
        sql<number>`count(distinct ${bid.id}) filter (where ${bid.status} = 'ACCEPTED')`.as(
          "acceptedBids",
        ),

      // Financial statistics
      totalJobBudget: sql<number>`sum(distinct ${job.budget})`.as(
        "totalJobBudget",
      ),
      avgJobBudget: sql<number>`avg(distinct ${job.budget})`.as("avgJobBudget"),
      totalBidValue: sql<number>`sum(${bid.amount})`.as("totalBidValue"),
      avgBidAmount: sql<number>`avg(${bid.amount})`.as("avgBidAmount"),

      // Savings calculation (budget vs accepted bid amounts)
      potentialSavings: sql<number>`
        coalesce(sum(distinct ${job.budget}) - sum(${bid.amount}) filter (where ${bid.status} = 'ACCEPTED'), 0)
      `.as("potentialSavings"),
    })
    .from(property)
    .leftJoin(job, eq(property.id, job.propertyId))
    .leftJoin(bid, eq(job.id, bid.jobId))
    .where(eq(property.userId, userId))
    .groupBy(property.userId);

  return (
    stats || {
      totalProperties: 0,
      totalJobs: 0,
      activeJobs: 0,
      awardedJobs: 0,
      completedJobs: 0,
      draftJobs: 0,
      totalBids: 0,
      pendingBids: 0,
      acceptedBids: 0,
      totalJobBudget: 0,
      avgJobBudget: 0,
      totalBidValue: 0,
      avgBidAmount: 0,
      potentialSavings: 0,
    }
  );
}
