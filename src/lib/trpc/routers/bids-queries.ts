import { and, desc, eq, sql } from "drizzle-orm";
import type { Db } from "@/db";
import { address, type BidStatus, bid, job, property } from "@/db/schema";

/**
 * Bids-specific optimized queries
 *
 * These functions provide optimized database queries specifically for the bids router.
 * They eliminate N+1 query patterns and provide comprehensive bid data in single queries.
 */

/**
 * OPTIMIZED: Get bids for a specific job with all related data
 *
 * BEFORE: Multiple queries for bid details, job info, property info
 * AFTER: Single query with all required data
 */
export async function getOptimizedJobBids(db: Db, jobId: string) {
  return db
    .select({
      // Bid data
      id: bid.id,
      amount: bid.amount,
      status: bid.status,
      createdAt: bid.createdAt,
      updatedAt: bid.updatedAt,
      organizationId: bid.organizationId,

      // Job data (for context)
      jobId: job.id,
      jobName: job.name,
      jobBudget: job.budget,
      jobStatus: job.status,

      // Property data (for location context)
      propertyName: property.name,
      propertyImageUrl: property.imageUrl,

      // Address data
      addressStreet: address.street,
      addressCity: address.city,
      addressState: address.state,
      addressZip: address.zip,

      // Competition metrics
      totalBids: sql<number>`
        (SELECT count(*) FROM ${bid} b2 WHERE b2.job_id = ${job.id})
      `.as("totalBids"),

      isLowestBid: sql<boolean>`
        ${bid.amount} = (SELECT min(amount) FROM ${bid} b3 WHERE b3.job_id = ${job.id})
      `.as("isLowestBid"),

      isHighestBid: sql<boolean>`
        ${bid.amount} = (SELECT max(amount) FROM ${bid} b4 WHERE b4.job_id = ${job.id})
      `.as("isHighestBid"),

      bidRank: sql<number>`
        rank() over (partition by ${job.id} order by ${bid.amount} asc)
      `.as("bidRank"),

      // Savings calculation
      potentialSavings: sql<number>`
        case
          when ${job.budget} is not null and ${bid.amount} < ${job.budget}
          then ${job.budget} - ${bid.amount}
          else 0
        end
      `.as("potentialSavings"),
    })
    .from(bid)
    .innerJoin(job, eq(bid.jobId, job.id))
    .innerJoin(property, eq(job.propertyId, property.id))
    .innerJoin(address, eq(property.addressId, address.id))
    .where(eq(bid.jobId, jobId))
    .orderBy(desc(bid.createdAt));
}

/**
 * OPTIMIZED: Get user's bids with comprehensive data
 *
 * BEFORE: N+1 queries for each bid's job and property details
 * AFTER: Single query with all required data
 */
export async function getOptimizedUserBids(
  db: Db,
  options: {
    status?: BidStatus;
    limit?: number;
    offset?: number;
  } = {},
) {
  const { status, limit = 20, offset = 0 } = options;

  const query = db
    .select({
      // Bid data
      id: bid.id,
      amount: bid.amount,
      status: bid.status,
      createdAt: bid.createdAt,
      updatedAt: bid.updatedAt,

      // Job data
      jobId: job.id,
      jobName: job.name,
      jobBudget: job.budget,
      jobStatus: job.status,
      jobDeadline: job.deadline,
      jobStartsAt: job.startsAt,

      // Property data
      propertyId: property.id,
      propertyName: property.name,
      propertyImageUrl: property.imageUrl,

      // Address data
      addressStreet: address.street,
      addressCity: address.city,
      addressState: address.state,
      addressZip: address.zip,

      // Competition data
      totalBids: sql<number>`
        (SELECT count(*) FROM ${bid} b2 WHERE b2.job_id = ${job.id})
      `.as("totalBids"),

      lowestBid: sql<number>`
        (SELECT min(amount) FROM ${bid} b3 WHERE b3.job_id = ${job.id})
      `.as("lowestBid"),

      isLowestBid: sql<boolean>`
        ${bid.amount} = (SELECT min(amount) FROM ${bid} b4 WHERE b4.job_id = ${job.id})
      `.as("isLowestBid"),

      // Win probability (simplified calculation)
      winProbability: sql<number>`
        case
          when ${bid.status} = 'ACCEPTED' then 100
          when ${bid.amount} = (SELECT min(amount) FROM ${bid} b5 WHERE b5.job_id = ${job.id}) then 80
          when ${bid.amount} <= (SELECT avg(amount) FROM ${bid} b6 WHERE b6.job_id = ${job.id}) then 60
          else 30
        end
      `.as("winProbability"),
    })
    .from(bid)
    .innerJoin(job, eq(bid.jobId, job.id))
    .innerJoin(property, eq(job.propertyId, property.id))
    .innerJoin(address, eq(property.addressId, address.id))
    .where(and(status ? eq(bid.status, status) : undefined))
    .orderBy(desc(bid.createdAt))
    .limit(limit)
    .offset(offset);

  return query;
}

/**
 * Get bid statistics for a user or organization
 */
export async function getBidStatistics(db: Db, organizationId: string) {
  const [stats] = await db
    .select({
      totalBids: sql<number>`count(*)`.as("totalBids"),
      acceptedBids: sql<number>`count(*) filter (where status = 'ACCEPTED')`.as(
        "acceptedBids",
      ),
      proposedBids: sql<number>`count(*) filter (where status = 'PROPOSED')`.as(
        "proposedBids",
      ),
      rejectedBids: sql<number>`count(*) filter (where status = 'REJECTED')`.as(
        "rejectedBids",
      ),

      totalBidValue: sql<number>`sum(amount)`.as("totalBidValue"),
      avgBidAmount: sql<number>`avg(amount)`.as("avgBidAmount"),
      acceptedBidValue:
        sql<number>`sum(amount) filter (where status = 'ACCEPTED')`.as(
          "acceptedBidValue",
        ),

      winRate: sql<number>`
        case
          when count(*) > 0
          then round((count(*) filter (where status = 'ACCEPTED')::decimal / count(*)) * 100, 2)
          else 0
        end
      `.as("winRate"),

      avgTimeToAcceptance: sql<number>`
        avg(extract(epoch from (updated_at - created_at)) / 3600) filter (where status = 'ACCEPTED')
      `.as("avgTimeToAcceptance"), // in hours
    })
    .from(bid)
    .where(eq(bid.organizationId, organizationId));

  return (
    stats || {
      totalBids: 0,
      acceptedBids: 0,
      proposedBids: 0,
      rejectedBids: 0,
      totalBidValue: 0,
      avgBidAmount: 0,
      acceptedBidValue: 0,
      winRate: 0,
      avgTimeToAcceptance: 0,
    }
  );
}
