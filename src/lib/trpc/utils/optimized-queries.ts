import { and, asc, desc, eq, inArray, isNull, sql } from "drizzle-orm";
import type { Db } from "@/db";
import {
  account,
  address,
  type Bid,
  bid,
  job,
  membership,
  organization,
  property,
  review,
  trade,
  user,
} from "@/db/schema";
import { transformUserJobsToNested } from "./transform-helpers";

/**
 * Optimized Query Implementations
 *
 * This file contains optimized versions of the most problematic queries
 * found in the tRPC routers. Each function replaces multiple inefficient
 * queries with a single optimized query.
 */

// ============================================================================
// JOBS ROUTER OPTIMIZATIONS
// ============================================================================

/**
 * OPTIMIZED: Replace jobs/listing.ts listForUser query
 *
 * BEFORE: Multiple queries + N+1 pattern
 * AFTER: Single query with all data
 */
export async function getOptimizedUserJobs(db: Db, userId: string) {
  const jobs = await db
    .select({
      // Job data
      id: job.id,
      name: job.name,
      budget: job.budget,
      status: job.status,
      createdAt: job.createdAt,
      startsAt: job.startsAt,
      deadline: job.deadline,
      completedAt: job.completedAt,
      updatedAt: job.updatedAt,
      jobType: job.jobType,
      taskBids: job.taskBids,
      isRecurring: job.isRecurring,
      contractorCompleted: job.contractorCompleted,
      homeownerCompleted: job.homeownerCompleted,
      recurringFrequency: job.recurringFrequency,

      // Property data (flattened)
      propertyId: property.id,
      propertyName: property.name,
      propertyCreatedAt: property.createdAt,
      propertyUpdatedAt: property.updatedAt,
      propertyImageUrl: property.imageUrl,
      propertyUserId: property.userId,
      propertyAddressId: property.addressId,
      propertyDescription: property.description,
      propertyType: property.type,
      propertyDeletedAt: property.deletedAt,

      // Address data (flattened) - excluding PostGIS location column
      addressId: address.id,
      addressStreet: address.street,
      addressCity: address.city,
      addressState: address.state,
      addressZip: address.zip,
      addressCreatedAt: address.createdAt,
      addressUpdatedAt: address.updatedAt,

      // Aggregated bid data
      bidsCount: sql<number>`count(${bid.id})`.as("bidsCount"),
      acceptedBid: sql<Bid>`
        json_agg(
          json_build_object(
            'id', ${bid.id},
            'amount', ${bid.amount},
            'status', ${bid.status},
            'organizationName', ${organization.name}
          )
        ) FILTER (WHERE ${bid.status} = 'ACCEPTED')
      `.as("acceptedBid"),
      hasAcceptedBid: sql<boolean>`bool_or(${bid.status} = 'ACCEPTED')`.as(
        "hasAcceptedBid",
      ),
    })
    .from(job)
    .innerJoin(property, eq(job.propertyId, property.id))
    .innerJoin(address, eq(property.addressId, address.id))
    .leftJoin(bid, eq(job.id, bid.jobId))
    .leftJoin(organization, eq(bid.organizationId, organization.id))
    .where(eq(property.userId, userId))
    .groupBy(job.id, property.id, address.id)
    .orderBy(desc(job.createdAt));

  return transformUserJobsToNested(jobs);
}

/**
 * OPTIMIZED: Replace jobs/listing.ts listPublished query
 *
 * BEFORE: Simple query but missing optimization for large datasets
 * AFTER: Optimized with pagination and proper indexing
 */
export async function getOptimizedPublishedJobs(
  db: Db,
  limit = 20,
  cursor?: string,
) {
  const whereConditions = [eq(job.status, "PUBLISHED")];

  if (cursor) {
    whereConditions.push(sql`${job.createdAt} < ${cursor}`);
  }

  return db
    .select({
      id: job.id,
      name: job.name,
      budget: job.budget,
      status: job.status,
      createdAt: job.createdAt,
      startsAt: job.startsAt,
      deadline: job.deadline,

      // Property data (flattened)
      propertyId: property.id,
      propertyName: property.name,

      // Address data (flattened)
      addressStreet: address.street,
      addressCity: address.city,
      addressState: address.state,
      addressZip: address.zip,

      bidsCount: sql<number>`count(${bid.id})`.as("bidsCount"),
      avgBidAmount: sql<number>`avg(${bid.amount})`.as("avgBidAmount"),
    })
    .from(job)
    .innerJoin(property, eq(job.propertyId, property.id))
    .innerJoin(address, eq(property.addressId, address.id))
    .leftJoin(bid, eq(job.id, bid.jobId))
    .where(and(...whereConditions))
    .groupBy(job.id, property.id, address.id)
    .orderBy(desc(job.createdAt))
    .limit(limit + 1); // +1 for pagination
}

// ============================================================================
// BIDS ROUTER OPTIMIZATIONS
// ============================================================================

/**
 * OPTIMIZED: Replace bids.ts listForOrganization query
 *
 * BEFORE: Complex nested relations causing N+1 queries
 * AFTER: Single optimized query with all required data
 */
export async function getOptimizedOrganizationBids(
  db: Db,
  organizationId: string,
) {
  return db
    .select({
      // Bid data
      id: bid.id,
      amount: bid.amount,
      status: bid.status,
      createdAt: bid.createdAt,

      // Job data (flattened)
      jobId: job.id,
      jobName: job.name,
      jobBudget: job.budget,
      jobStatus: job.status,
      jobStartsAt: job.startsAt,
      jobDeadline: job.deadline,

      // Property data (flattened)
      propertyName: property.name,

      // Address data (flattened)
      addressStreet: address.street,
      addressCity: address.city,
      addressState: address.state,

      // Competition data
      totalBids: sql<number>`
        (SELECT count(*) FROM ${bid} b2 WHERE b2.job_id = ${job.id})
      `.as("totalBids"),

      isLowestBid: sql<boolean>`
        ${bid.amount} = (SELECT min(amount) FROM ${bid} b3 WHERE b3.job_id = ${job.id})
      `.as("isLowestBid"),
    })
    .from(bid)
    .innerJoin(job, eq(bid.jobId, job.id))
    .innerJoin(property, eq(job.propertyId, property.id))
    .innerJoin(address, eq(property.addressId, address.id))
    .where(eq(bid.organizationId, organizationId))
    .orderBy(desc(bid.createdAt));
}

// ============================================================================
// CONTRACTOR ROUTER OPTIMIZATIONS
// ============================================================================

/**
 * OPTIMIZED: Replace contractor/search.ts search query
 *
 * BEFORE: ILIKE queries (slow on large datasets)
 * AFTER: Full-text search with ranking
 */
export async function getOptimizedContractorSearch(
  db: Db,
  searchQuery: string,
  excludeIds: string[] = [],
  limit = 10,
) {
  const tsQuery = searchQuery.trim().split(" ").join(" & ");

  const whereConditions = [
    sql`(
      to_tsvector('english', ${organization.name}) @@ to_tsquery('english', ${tsQuery}) OR
      to_tsvector('english', ${trade.name}) @@ to_tsquery('english', ${tsQuery}) OR
      to_tsvector('english', coalesce(${organization.description}, '')) @@ to_tsquery('english', ${tsQuery})
    )`,
  ];

  if (excludeIds.length > 0) {
    whereConditions.push(
      sql`${organization.id} NOT IN (${sql.join(
        excludeIds.map((id) => sql`${id}`),
        sql`, `,
      )})`,
    );
  }

  return db
    .select({
      id: organization.id,
      name: organization.name,
      description: organization.description,
      logoUrl: organization.logoUrl,
      email: organization.email,
      phone: organization.phone,
      acceptsQuickHire: organization.acceptsQuickHire,

      // Trade data (flattened)
      tradeId: trade.id,
      tradeName: trade.name,

      // Address data (flattened)
      addressStreet: address.street,
      addressCity: address.city,
      addressState: address.state,
      addressZip: address.zip,

      // Statistics
      memberCount: sql<number>`count(distinct ${membership.userId})`.as(
        "memberCount",
      ),
      completedJobsCount: sql<number>`
        count(distinct ${job.id}) filter (where ${job.status} = 'COMPLETED')
      `.as("completedJobsCount"),
      avgRating: sql<number>`avg(${review.rating})`.as("avgRating"),
      reviewCount: sql<number>`count(distinct ${review.id})`.as("reviewCount"),

      // Search ranking
      searchRank: sql<number>`
        ts_rank(
          to_tsvector('english', ${organization.name} || ' ' || coalesce(${organization.description}, '')),
          to_tsquery('english', ${tsQuery})
        )
      `.as("searchRank"),
    })
    .from(organization)
    .leftJoin(trade, eq(organization.tradeId, trade.id))
    .leftJoin(address, eq(organization.addressId, address.id))
    .leftJoin(membership, eq(organization.id, membership.organizationId))
    .leftJoin(
      bid,
      and(eq(organization.id, bid.organizationId), eq(bid.status, "ACCEPTED")),
    )
    .leftJoin(job, and(eq(bid.jobId, job.id), eq(job.status, "COMPLETED")))
    .leftJoin(
      review,
      and(eq(review.jobId, job.id), eq(review.reviewType, "CONTRACTOR_REVIEW")),
    )
    .where(and(...whereConditions))
    .groupBy(organization.id, trade.id, address.id)
    .orderBy(
      desc(sql`avg(${review.rating})`),
      desc(sql`ts_rank(
        to_tsvector('english', ${organization.name} || ' ' || coalesce(${organization.description}, '')),
        to_tsquery('english', ${tsQuery})
      )`),
      asc(organization.name),
    )
    .limit(limit);
}

// ============================================================================
// ADMIN ROUTER OPTIMIZATIONS
// ============================================================================

/**
 * OPTIMIZED: Replace admin.ts getStats multiple queries
 *
 * BEFORE: 6+ separate count queries
 * AFTER: Single query with all statistics
 */
export async function getOptimizedAdminStats(db: Db) {
  const [stats] = await db
    .select({
      // User statistics
      totalUsers: sql<number>`count(distinct ${account.id})`.as("totalUsers"),

      // Job statistics
      totalJobs: sql<number>`count(distinct ${job.id})`.as("totalJobs"),
      draftJobs:
        sql<number>`count(distinct ${job.id}) filter (where ${job.status} = 'DRAFT')`.as(
          "draftJobs",
        ),
      publishedJobs:
        sql<number>`count(distinct ${job.id}) filter (where ${job.status} = 'PUBLISHED')`.as(
          "publishedJobs",
        ),
      awardedJobs:
        sql<number>`count(distinct ${job.id}) filter (where ${job.status} = 'AWARDED')`.as(
          "awardedJobs",
        ),
      completedJobs:
        sql<number>`count(distinct ${job.id}) filter (where ${job.status} = 'COMPLETED')`.as(
          "completedJobs",
        ),

      // Bid statistics
      totalBids: sql<number>`count(distinct ${bid.id})`.as("totalBids"),
      acceptedBids:
        sql<number>`count(distinct ${bid.id}) filter (where ${bid.status} = 'ACCEPTED')`.as(
          "acceptedBids",
        ),

      // Organization statistics
      totalOrganizations: sql<number>`count(distinct ${organization.id})`.as(
        "totalOrganizations",
      ),

      // Financial statistics
      totalJobValue: sql<number>`sum(distinct ${job.budget})`.as(
        "totalJobValue",
      ),
      avgJobBudget: sql<number>`avg(distinct ${job.budget})`.as("avgJobBudget"),
      totalBidValue: sql<number>`sum(${bid.amount})`.as("totalBidValue"),
      avgBidAmount: sql<number>`avg(${bid.amount})`.as("avgBidAmount"),
    })
    .from(account)
    .fullJoin(job, sql`true`)
    .fullJoin(bid, sql`true`)
    .fullJoin(organization, sql`true`);

  return stats;
}

// ============================================================================
// REVIEWS ROUTER OPTIMIZATIONS
// ============================================================================

/**
 * OPTIMIZED: Replace reviews.ts listForOrganizations query
 *
 * BEFORE: Multiple queries with complex filtering
 * AFTER: Single optimized query with proper joins
 */
export async function getOptimizedOrganizationReviews(
  db: Db,
  organizationIds: string[],
) {
  if (organizationIds.length === 0) return [];

  return db
    .select({
      id: review.id,
      rating: review.rating,
      comment: review.comment,
      reviewType: review.reviewType,
      createdAt: review.createdAt,

      job: {
        id: job.id,
        name: job.name,
        completedAt: job.completedAt,
      },

      organizationId: bid.organizationId,

      // Reviewer info (property owner)
      reviewer: {
        name: user.name,
      },
    })
    .from(review)
    .innerJoin(job, eq(review.jobId, job.id))
    .innerJoin(
      bid,
      and(
        eq(bid.jobId, job.id),
        eq(bid.status, "ACCEPTED"),
        inArray(bid.organizationId, organizationIds),
      ),
    )
    .innerJoin(property, eq(job.propertyId, property.id))
    .innerJoin(user, eq(property.userId, user.id))
    .where(
      and(
        eq(job.status, "COMPLETED"),
        eq(review.reviewType, "CONTRACTOR_REVIEW"),
      ),
    )
    .orderBy(desc(review.createdAt));
}

// ============================================================================
// DASHBOARD ROUTER OPTIMIZATIONS
// ============================================================================

/**
 * OPTIMIZED: Replace dashboard.ts getContractorDashboardStats multiple queries
 *
 * BEFORE: 4+ separate count queries
 * AFTER: Single query with all contractor statistics
 */
export async function getOptimizedContractorDashboardStats(
  db: Db,
  organizationId: string,
) {
  const [stats] = await db
    .select({
      // Bid statistics
      totalBids: sql<number>`count(distinct ${bid.id})`.as("totalBids"),
      acceptedBids:
        sql<number>`count(distinct ${bid.id}) filter (where ${bid.status} = 'ACCEPTED')`.as(
          "acceptedBids",
        ),
      proposedBids:
        sql<number>`count(distinct ${bid.id}) filter (where ${bid.status} = 'PROPOSED')`.as(
          "proposedBids",
        ),

      // Job statistics
      activeJobs:
        sql<number>`count(distinct ${job.id}) filter (where ${job.status} = 'AWARDED' and ${bid.status} = 'ACCEPTED')`.as(
          "activeJobs",
        ),
      completedJobs:
        sql<number>`count(distinct ${job.id}) filter (where ${job.status} = 'COMPLETED' and ${bid.status} = 'ACCEPTED')`.as(
          "completedJobs",
        ),

      // Financial statistics
      totalBidValue: sql<number>`sum(${bid.amount})`.as("totalBidValue"),
      avgBidAmount: sql<number>`avg(${bid.amount})`.as("avgBidAmount"),
      acceptedBidValue:
        sql<number>`sum(${bid.amount}) filter (where ${bid.status} = 'ACCEPTED')`.as(
          "acceptedBidValue",
        ),

      // Performance metrics
      winRate: sql<number>`
        case
          when count(distinct ${bid.id}) > 0
          then round((count(distinct ${bid.id}) filter (where ${bid.status} = 'ACCEPTED')::decimal / count(distinct ${bid.id})) * 100, 2)
          else 0
        end
      `.as("winRate"),

      avgRating: sql<number>`avg(${review.rating})`.as("avgRating"),
      reviewCount: sql<number>`count(distinct ${review.id})`.as("reviewCount"),
    })
    .from(organization)
    .leftJoin(bid, eq(organization.id, bid.organizationId))
    .leftJoin(job, eq(bid.jobId, job.id))
    .leftJoin(
      review,
      and(eq(review.jobId, job.id), eq(review.reviewType, "CONTRACTOR_REVIEW")),
    )
    .where(eq(organization.id, organizationId))
    .groupBy(organization.id);

  return (
    stats || {
      totalBids: 0,
      acceptedBids: 0,
      proposedBids: 0,
      activeJobs: 0,
      completedJobs: 0,
      totalBidValue: 0,
      avgBidAmount: 0,
      acceptedBidValue: 0,
      winRate: 0,
      avgRating: 0,
      reviewCount: 0,
    }
  );
}

/**
 * OPTIMIZED: Replace dashboard.ts getHomeownerDashboardStats multiple queries
 *
 * BEFORE: 4+ separate count queries
 * AFTER: Single query with all homeowner statistics
 */
export async function getOptimizedHomeownerDashboardStats(
  db: Db,
  userId: string,
) {
  const [stats] = await db
    .select({
      // Property statistics
      totalProperties: sql<number>`count(distinct ${property.id})`.as(
        "totalProperties",
      ),

      // Job statistics
      totalJobs: sql<number>`count(distinct ${job.id})`.as("totalJobs"),
      activeJobs:
        sql<number>`count(distinct ${job.id}) filter (where ${job.status} = 'PUBLISHED')`.as(
          "activeJobs",
        ),
      awardedJobs:
        sql<number>`count(distinct ${job.id}) filter (where ${job.status} = 'AWARDED')`.as(
          "awardedJobs",
        ),
      completedJobs:
        sql<number>`count(distinct ${job.id}) filter (where ${job.status} = 'COMPLETED')`.as(
          "completedJobs",
        ),
      draftJobs:
        sql<number>`count(distinct ${job.id}) filter (where ${job.status} = 'DRAFT')`.as(
          "draftJobs",
        ),

      // Bid statistics
      totalBids: sql<number>`count(distinct ${bid.id})`.as("totalBids"),
      pendingBids:
        sql<number>`count(distinct ${bid.id}) filter (where ${bid.status} = 'PROPOSED')`.as(
          "pendingBids",
        ),
      acceptedBids:
        sql<number>`count(distinct ${bid.id}) filter (where ${bid.status} = 'ACCEPTED')`.as(
          "acceptedBids",
        ),

      // Financial statistics
      totalJobBudget: sql<number>`sum(distinct ${job.budget})`.as(
        "totalJobBudget",
      ),
      avgJobBudget: sql<number>`avg(distinct ${job.budget})`.as("avgJobBudget"),
      totalBidValue: sql<number>`sum(${bid.amount})`.as("totalBidValue"),
      avgBidAmount: sql<number>`avg(${bid.amount})`.as("avgBidAmount"),

      // Savings calculation (budget vs accepted bids)
      potentialSavings: sql<number>`
        coalesce(sum(distinct ${job.budget}) - sum(${bid.amount}) filter (where ${bid.status} = 'ACCEPTED'), 0)
      `.as("potentialSavings"),
    })
    .from(property)
    .leftJoin(job, eq(property.id, job.propertyId))
    .leftJoin(bid, eq(job.id, bid.jobId))
    .where(eq(property.userId, userId))
    .groupBy(property.userId);

  return (
    stats || {
      totalProperties: 0,
      totalJobs: 0,
      activeJobs: 0,
      awardedJobs: 0,
      completedJobs: 0,
      draftJobs: 0,
      totalBids: 0,
      pendingBids: 0,
      acceptedBids: 0,
      totalJobBudget: 0,
      avgJobBudget: 0,
      totalBidValue: 0,
      avgBidAmount: 0,
      potentialSavings: 0,
    }
  );
}

// ============================================================================
// MESSAGES ROUTER OPTIMIZATIONS
// ============================================================================

/**
 * OPTIMIZED: Replace messages/message.ts listMessages query
 *
 * BEFORE: Multiple queries with complex permission checks
 * AFTER: Single optimized query with all message data and metadata
 */
export async function getOptimizedMessagesList(
  db: Db,
  chatId: string,
  limit = 50,
  cursor?: string,
) {
  const { message, chat } = await import("@/db/schema");

  const whereConditions = [eq(message.chatId, chatId)];

  if (cursor) {
    whereConditions.push(sql`${message.createdAt} < ${cursor}`);
  }

  return db
    .select({
      id: message.id,
      content: message.content,
      senderId: message.senderId,
      senderType: message.senderType,
      senderInitials: message.senderInitials,
      senderAvatarUrl: message.senderAvatarUrl,
      createdAt: message.createdAt,
      isCommand: message.isCommand,
      commandData: message.commandData,

      // Chat metadata
      chatId: chat.id,
      chatBidId: chat.bidId,
      chatJobId: chat.jobId,
    })
    .from(message)
    .innerJoin(chat, eq(message.chatId, chat.id))
    .where(and(...whereConditions))
    .orderBy(desc(message.createdAt))
    .limit(limit + 1); // +1 for pagination
}

// ============================================================================
// PROPERTIES ROUTER OPTIMIZATIONS
// ============================================================================

/**
 * OPTIMIZED: Replace properties.ts list query with aggregated data
 *
 * BEFORE: Simple property list without statistics
 * AFTER: Properties with job counts, bid statistics, and completion rates
 */
export async function getOptimizedPropertiesList(db: Db, userId: string) {
  const properties = await db
    .select({
      // Property data
      id: property.id,
      name: property.name,
      imageUrl: property.imageUrl,
      createdAt: property.createdAt,
      updatedAt: property.updatedAt,
      description: property.description,
      type: property.type,
      userId: property.userId,
      deletedAt: property.deletedAt,

      // Address data (flattened)
      addressId: address.id,
      addressStreet: address.street,
      addressCity: address.city,
      addressState: address.state,
      addressZip: address.zip,
      addressCreatedAt: address.createdAt,
      addressUpdatedAt: address.updatedAt,
      addressLocation: address.location,

      // Job statistics
      totalJobs: sql<number>`count(distinct ${job.id})`.as("totalJobs"),
      activeJobs:
        sql<number>`count(distinct ${job.id}) filter (where ${job.status} = 'PUBLISHED')`.as(
          "activeJobs",
        ),
      completedJobs:
        sql<number>`count(distinct ${job.id}) filter (where ${job.status} = 'COMPLETED')`.as(
          "completedJobs",
        ),
      draftJobs:
        sql<number>`count(distinct ${job.id}) filter (where ${job.status} = 'DRAFT')`.as(
          "draftJobs",
        ),

      // Financial statistics
      totalBudget: sql<number>`sum(${job.budget})`.as("totalBudget"),
      avgJobBudget: sql<number>`avg(${job.budget})`.as("avgJobBudget"),

      // Bid statistics
      totalBids: sql<number>`count(distinct ${bid.id})`.as("totalBids"),
      avgBidsPerJob: sql<number>`
        case
          when count(distinct ${job.id}) > 0
          then round(count(distinct ${bid.id})::decimal / count(distinct ${job.id}), 2)
          else 0
        end
      `.as("avgBidsPerJob"),

      // Latest activity
      lastJobCreated: sql<Date>`max(${job.createdAt})`.as("lastJobCreated"),
      lastJobCompleted: sql<Date>`max(${job.completedAt})`.as(
        "lastJobCompleted",
      ),
    })
    .from(property)
    .innerJoin(address, eq(property.addressId, address.id))
    .leftJoin(job, eq(property.id, job.propertyId))
    .leftJoin(bid, eq(job.id, bid.jobId))
    .where(
      and(
        eq(property.userId, userId),
        // Filter out deleted properties
        isNull(property.deletedAt),
      ),
    )
    .groupBy(property.id, address.id)
    .orderBy(desc(property.createdAt));

  return properties.map((property) => ({
    ...property,
    address: {
      street: property.addressStreet,
      city: property.addressCity,
      state: property.addressState,
      zip: property.addressZip,
      createdAt: property.addressCreatedAt,
      updatedAt: property.addressUpdatedAt,
      location: property.addressLocation,
      id: property.addressId,
    },
  }));
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Get organization statistics in a single query
 */
export async function getOrganizationStats(db: Db, organizationId: string) {
  const [stats] = await db
    .select({
      totalJobs: sql<number>`count(distinct ${job.id})`.as("totalJobs"),
      completedJobs:
        sql<number>`count(distinct ${job.id}) filter (where ${job.status} = 'COMPLETED')`.as(
          "completedJobs",
        ),
      activeJobs:
        sql<number>`count(distinct ${job.id}) filter (where ${job.status} = 'PUBLISHED' and ${bid.status} = 'ACCEPTED')`.as(
          "activeJobs",
        ),
      totalBids: sql<number>`count(distinct ${bid.id})`.as("totalBids"),
      acceptedBids:
        sql<number>`count(distinct ${bid.id}) filter (where ${bid.status} = 'ACCEPTED')`.as(
          "acceptedBids",
        ),
      avgRating: sql<number>`avg(${review.rating})`.as("avgRating"),
      reviewCount: sql<number>`count(distinct ${review.id})`.as("reviewCount"),
      memberCount: sql<number>`count(distinct ${membership.userId})`.as(
        "memberCount",
      ),
    })
    .from(organization)
    .leftJoin(bid, eq(organization.id, bid.organizationId))
    .leftJoin(job, eq(bid.jobId, job.id))
    .leftJoin(
      review,
      and(eq(review.jobId, job.id), eq(review.reviewType, "CONTRACTOR_REVIEW")),
    )
    .leftJoin(membership, eq(organization.id, membership.organizationId))
    .where(eq(organization.id, organizationId))
    .groupBy(organization.id);

  return stats;
}
