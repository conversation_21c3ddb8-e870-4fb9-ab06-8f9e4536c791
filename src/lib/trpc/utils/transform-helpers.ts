/**
 * Helper functions to transform flattened query results back to nested objects
 * when needed for backwards compatibility or specific UI requirements.
 */

import type { Bid, Job, JobStatus, JobType } from "@/db/schema";

type FlattenedUserJob = {
  id: string;
  name: string;
  budget: number;
  status: JobStatus;
  createdAt: Date;
  startsAt: Date;
  deadline: Date;
  completedAt: Date | null;
  updatedAt: Date;
  jobType: JobType;
  taskBids: boolean;
  isRecurring: boolean;
  contractorCompleted: boolean;
  homeownerCompleted: boolean;
  recurringFrequency: string | null;

  // Flattened property data
  propertyId: string;
  propertyName: string;
  propertyCreatedAt: Date;
  propertyUpdatedAt: Date;
  propertyImageUrl: string | null;
  propertyUserId: string;
  propertyAddressId: string;
  propertyDescription: string | null;
  propertyType: string | null;
  propertyDeletedAt: Date | null;

  // Flattened address data (excluding PostGIS location for serialization)
  addressId: string;
  addressStreet: string;
  addressCity: string;
  addressState: string;
  addressZip: string;
  addressCreatedAt: Date;
  addressUpdatedAt: Date;

  // Aggregated data
  bidsCount: number;
  acceptedBid: Bid;
  hasAcceptedBid: boolean;
};

interface NestedUserJob extends Job {
  id: string;
  name: string;
  budget: number;
  status: JobStatus;
  createdAt: Date;
  startsAt: Date;
  deadline: Date;
  completedAt: Date | null;
  updatedAt: Date;
  jobType: JobType;
  taskBids: boolean;
  isRecurring: boolean;
  contractorCompleted: boolean;
  homeownerCompleted: boolean;
  propertyId: string;
  recurringFrequency: string | null;

  property: {
    id: string;
    name: string;
    createdAt: Date;
    updatedAt: Date;
    imageUrl: string | null;
    userId: string;
    addressId: string;
    description: string | null;
    type: string | null;
    deletedAt: Date | null;

    address: {
      id: string;
      street: string;
      city: string;
      state: string;
      zip: string;
      createdAt: Date;
      updatedAt: Date;
      location: { x: number; y: number } | null;
    };
  };

  bidsCount: number;
  acceptedBid: Bid;
  hasAcceptedBid: boolean;
}

/**
 * Transform flattened user job data back to nested structure
 */
export function transformUserJobToNested(
  flatJob: FlattenedUserJob,
): NestedUserJob {
  return {
    id: flatJob.id,
    name: flatJob.name,
    budget: flatJob.budget,
    status: flatJob.status,
    createdAt: flatJob.createdAt,
    startsAt: flatJob.startsAt,
    deadline: flatJob.deadline,
    completedAt: flatJob.completedAt,
    updatedAt: flatJob.updatedAt,
    jobType: flatJob.jobType,
    taskBids: flatJob.taskBids,
    isRecurring: flatJob.isRecurring,
    contractorCompleted: flatJob.contractorCompleted,
    homeownerCompleted: flatJob.homeownerCompleted,
    recurringFrequency: flatJob.recurringFrequency,
    propertyId: flatJob.propertyId,

    property: {
      id: flatJob.propertyId,
      name: flatJob.propertyName,
      createdAt: flatJob.propertyCreatedAt,
      updatedAt: flatJob.propertyUpdatedAt,
      imageUrl: flatJob.propertyImageUrl,
      userId: flatJob.propertyUserId,
      addressId: flatJob.propertyAddressId,
      description: flatJob.propertyDescription, // Not available in flattened data
      type: flatJob.propertyType, // Not available in flattened data
      deletedAt: flatJob.propertyDeletedAt, // Not available in flattened data

      address: {
        id: flatJob.addressId,
        street: flatJob.addressStreet,
        city: flatJob.addressCity,
        state: flatJob.addressState,
        zip: flatJob.addressZip,
        createdAt: flatJob.addressCreatedAt,
        updatedAt: flatJob.addressUpdatedAt,
        location: null,
      },
    },

    bidsCount: flatJob.bidsCount,
    acceptedBid: flatJob.acceptedBid,
    hasAcceptedBid: flatJob.hasAcceptedBid,
  };
}

/**
 * Transform array of flattened jobs to nested structure
 */
export function transformUserJobsToNested(
  flatJobs: FlattenedUserJob[],
): NestedUserJob[] {
  return flatJobs.map(transformUserJobToNested);
}

// Similar transform functions can be created for other entities as needed
