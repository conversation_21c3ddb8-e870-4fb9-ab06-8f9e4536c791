import { z } from "zod/v4";

export const waitlistSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.email("Please enter a valid email address"),
  reason: z.string().optional(),
  accountType: z.enum(["homeowner", "contractor"]),
});

export type WaitlistFormData = z.infer<typeof waitlistSchema>;

export const waitlistAdminSchema = z.object({
  id: z.string(),
  status: z.enum(["PENDING", "APPROVED", "REJECTED", "INVITED"]),
  inviteToken: z.string().optional(),
  inviteExpires: z.date().optional(),
});

export type WaitlistAdminData = z.infer<typeof waitlistAdminSchema>;
