import { env } from "@/env";

/**
 * Formats the uploaded file URL using the storage URL from environment variables
 * This replaces the old formatUploadedFileUrl function from the Uppy system
 */
export function formatUploadedFileUrl(filename: string): string {
  // Remove leading slash if present
  const cleanFilename = filename.startsWith("/") ? filename.slice(1) : filename;

  // Ensure storage URL doesn't end with slash and filename doesn't start with slash
  const baseUrl = env.NEXT_PUBLIC_STORAGE_URL.endsWith("/")
    ? env.NEXT_PUBLIC_STORAGE_URL.slice(0, -1)
    : env.NEXT_PUBLIC_STORAGE_URL;

  return `${baseUrl}/${cleanFilename}`;
}

/**
 * Extracts the filename/key from a full URL
 */
export function extractFilenameFromUrl(url: string): string {
  try {
    const urlObj = new URL(url);
    return urlObj.pathname.startsWith("/")
      ? urlObj.pathname.slice(1)
      : urlObj.pathname;
  } catch {
    // If URL parsing fails, assume it's already a filename
    return url;
  }
}
