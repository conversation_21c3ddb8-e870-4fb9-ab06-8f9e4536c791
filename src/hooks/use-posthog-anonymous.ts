"use client";

import { usePostHog } from "posthog-js/react";
import { useCallback, useEffect, useState } from "react";
import { env } from "@/env";
import { posthogCookieUtils } from "@/lib/cookie-utils";
import { useSession } from "@/lib/auth-client";

/**
 * Hook for PostHog anonymous user tracking
 * Provides utilities for tracking anonymous users (like waitlist users) with persistent IDs
 */
export function usePostHogAnonymous() {
  const posthog = usePostHog();
  const { data: session } = useSession();
  const [anonymousId, setAnonymousId] = useState<string | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);

  // Initialize anonymous ID on mount
  useEffect(() => {
    if (typeof window !== "undefined" && !isInitialized) {
      const id = posthogCookieUtils.getOrCreateAnonymousId(env.NEXT_PUBLIC_POSTHOG_KEY);
      setAnonymousId(id);
      setIsInitialized(true);
    }
  }, [isInitialized]);

  /**
   * Capture an event for anonymous users
   * Automatically uses the anonymous ID if user is not authenticated
   */
  const captureAnonymous = useCallback(
    (eventName: string, properties?: Record<string, any>) => {
      if (!posthog) return;

      const eventProperties: Record<string, any> = {
        ...properties,
        $is_anonymous: !session?.user,
        $anonymous_id: anonymousId,
      };

      // Add waitlist context if user is not authenticated
      if (!session?.user) {
        eventProperties.$user_type = "anonymous";
        eventProperties.$is_waitlist_user = true;
      }

      posthog.capture(eventName, eventProperties);
    },
    [posthog, session, anonymousId]
  );

  /**
   * Identify an anonymous user (useful for waitlist signup)
   * This creates a person profile while maintaining the anonymous ID
   */
  const identifyAnonymous = useCallback(
    (properties?: Record<string, any>) => {
      if (!posthog || !anonymousId) return;

      const identifyProperties = {
        ...properties,
        $is_anonymous: true,
        $anonymous_id: anonymousId,
        $user_type: "anonymous",
      };

      posthog.identify(anonymousId, identifyProperties);
    },
    [posthog, anonymousId]
  );

  /**
   * Track waitlist signup
   * Special method for tracking waitlist signups with proper anonymous identification
   */
  const trackWaitlistSignup = useCallback(
    (email: string, additionalProperties?: Record<string, any>) => {
      if (!posthog || !anonymousId) return;

      // First identify the anonymous user with their email
      posthog.identify(anonymousId, {
        email,
        $is_anonymous: true,
        $user_type: "waitlist",
        $anonymous_id: anonymousId,
        ...additionalProperties,
      });

      // Then capture the waitlist signup event
      posthog.capture("waitlist_signup", {
        email,
        $is_anonymous: true,
        $user_type: "waitlist",
        $anonymous_id: anonymousId,
        ...additionalProperties,
      });
    },
    [posthog, anonymousId]
  );

  /**
   * Track feature flag evaluation for anonymous users
   */
  const trackFeatureFlag = useCallback(
    (flagKey: string, flagValue: any, properties?: Record<string, any>) => {
      if (!posthog) return;

      posthog.capture("$feature_flag_called", {
        $feature_flag: flagKey,
        $feature_flag_response: flagValue,
        $is_anonymous: !session?.user,
        $anonymous_id: anonymousId,
        ...properties,
      });
    },
    [posthog, session, anonymousId]
  );

  /**
   * Get feature flag value for anonymous users
   * Uses the anonymous ID for consistent flag evaluation
   */
  const getFeatureFlag = useCallback(
    (flagKey: string, defaultValue?: any) => {
      if (!posthog || !anonymousId) return defaultValue;

      // PostHog will use the current distinct_id automatically
      // We don't need to pass it explicitly
      return posthog.getFeatureFlag(flagKey) ?? defaultValue;
    },
    [posthog, anonymousId]
  );

  /**
   * Check if feature flag is enabled for anonymous users
   */
  const isFeatureEnabled = useCallback(
    (flagKey: string): boolean => {
      return getFeatureFlag(flagKey, false) === true;
    },
    [getFeatureFlag]
  );

  /**
   * Clear anonymous tracking data
   * Useful for privacy compliance or testing
   */
  const clearAnonymousData = useCallback(() => {
    posthogCookieUtils.clearAnonymousId();
    setAnonymousId(null);
    setIsInitialized(false);
  }, []);

  /**
   * Get the current anonymous ID
   */
  const getCurrentAnonymousId = useCallback(() => {
    return anonymousId;
  }, [anonymousId]);

  /**
   * Check if user is currently anonymous (not authenticated)
   */
  const isAnonymous = !session?.user;

  /**
   * Check if anonymous tracking is properly initialized
   */
  const isAnonymousTrackingReady = isInitialized && anonymousId !== null;

  return {
    // State
    anonymousId,
    isAnonymous,
    isAnonymousTrackingReady,

    // Methods
    captureAnonymous,
    identifyAnonymous,
    trackWaitlistSignup,
    trackFeatureFlag,
    getFeatureFlag,
    isFeatureEnabled,
    clearAnonymousData,
    getCurrentAnonymousId,
  };
}

/**
 * Utility type for anonymous user properties
 */
export type AnonymousUserProperties = {
  email?: string;
  source?: string;
  campaign?: string;
  referrer?: string;
  landing_page?: string;
  user_agent?: string;
  [key: string]: any;
};

/**
 * Utility type for waitlist signup properties
 */
export type WaitlistSignupProperties = AnonymousUserProperties & {
  email: string;
  signup_source?: "landing_page" | "modal" | "footer" | "nav" | "other";
  feature_interest?: string[];
  company_size?: string;
  role?: string;
};
