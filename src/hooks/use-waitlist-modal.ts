"use client";

import { useWaitlist } from "@/components/providers";
import { useCallback } from "react";
import { useRouter } from "next/navigation";

export function useWaitlistModal() {
  const { showWaitlistModal, isWaitlistEnabled } = useWaitlist();
  const router = useRouter();

  const handleSignUpClick = useCallback((
    e: React.MouseEvent<HTMLAnchorElement | HTMLButtonElement>,
    accountType?: "homeowner" | "contractor",
    href?: string
  ) => {
    if (isWaitlistEnabled) {
      e.preventDefault();
      showWaitlistModal(accountType);
    } else if (href) {
      // If waitlist is not enabled, navigate to the sign-up page
      router.push(href);
    }
  }, [isWaitlistEnabled, showWaitlistModal, router]);

  return {
    handleSignUpClick,
    isWaitlistEnabled,
  };
}