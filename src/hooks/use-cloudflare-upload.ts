import { useCallback, useState } from "react";
import { toast } from "sonner";

/**
 * Upload state interface
 */
export interface UploadState {
  isUploading: boolean;
  progress: number;
  error: string | null;
  uploadedUrl: string | null;
  key: string | null;
}

/**
 * Upload options
 */
export interface UploadOptions {
  uploadType?: "property-image" | "job-image" | "profile-image";
  onProgress?: (progress: number) => void;
  onSuccess?: (url: string, key: string) => void;
  onError?: (error: string) => void;
}

/**
 * Presigned URL response
 */
interface PresignedUrlResponse {
  presignedUrl: string;
  key: string;
  publicUrl: string;
  expiresIn: number;
}

/**
 * Hook for handling direct uploads to Cloudflare R2
 */
export function useCloudflareUpload(options: UploadOptions = {}) {
  const { uploadType = "job-image", onProgress, onSuccess, onError } = options;

  const [uploadState, setUploadState] = useState<UploadState>({
    isUploading: false,
    progress: 0,
    error: null,
    uploadedUrl: null,
    key: null,
  });

  /**
   * Reset upload state
   */
  const resetUpload = useCallback(() => {
    setUploadState({
      isUploading: false,
      progress: 0,
      error: null,
      uploadedUrl: null,
      key: null,
    });
  }, []);

  /**
   * Get presigned URL from API
   */
  const getPresignedUrl = useCallback(
    async (
      filename: string,
      contentType: string,
      fileSize: number,
    ): Promise<PresignedUrlResponse> => {
      const response = await fetch("/api/upload/presigned-url", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          filename,
          contentType,
          fileSize,
          uploadType,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to get presigned URL");
      }

      return response.json();
    },
    [uploadType],
  );

  /**
   * Upload file to Cloudflare R2 using presigned URL
   */
  const uploadToR2 = useCallback(
    async (
      presignedUrl: string,
      file: File,
      contentType: string,
    ): Promise<void> => {
      return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest();

        // Track upload progress
        xhr.upload.addEventListener("progress", (event) => {
          if (event.lengthComputable) {
            const progress = Math.round((event.loaded / event.total) * 100);
            setUploadState((prev) => ({ ...prev, progress }));
            onProgress?.(progress);
          }
        });

        // Handle completion
        xhr.addEventListener("load", () => {
          if (xhr.status >= 200 && xhr.status < 300) {
            resolve();
          } else {
            reject(new Error(`Upload failed with status ${xhr.status}`));
          }
        });

        // Handle errors
        xhr.addEventListener("error", () => {
          reject(new Error("Upload failed due to network error"));
        });

        // Handle abort
        xhr.addEventListener("abort", () => {
          reject(new Error("Upload was aborted"));
        });

        // Start upload
        xhr.open("PUT", presignedUrl);
        xhr.setRequestHeader("Content-Type", contentType);
        xhr.send(file);
      });
    },
    [onProgress],
  );

  /**
   * Main upload function
   */
  const uploadFile = useCallback(
    async (file: File): Promise<string | null> => {
      try {
        // Reset state
        setUploadState({
          isUploading: true,
          progress: 0,
          error: null,
          uploadedUrl: null,
          key: null,
        });

        // Get presigned URL
        const { presignedUrl, key, publicUrl } = await getPresignedUrl(
          file.name,
          file.type,
          file.size,
        );

        // Upload file
        await uploadToR2(presignedUrl, file, file.type);

        // Update state with success
        setUploadState({
          isUploading: false,
          progress: 100,
          error: null,
          uploadedUrl: publicUrl,
          key,
        });

        // Call success callback
        onSuccess?.(publicUrl, key);

        toast.success("File uploaded successfully");
        return publicUrl;
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Upload failed";

        // Update state with error
        setUploadState((prev) => ({
          ...prev,
          isUploading: false,
          error: errorMessage,
        }));

        // Call error callback
        onError?.(errorMessage);

        toast.error(`Upload failed: ${errorMessage}`);
        return null;
      }
    },
    [getPresignedUrl, uploadToR2, onSuccess, onError],
  );

  /**
   * Upload multiple files
   */
  const uploadFiles = useCallback(
    async (files: File[]): Promise<string[]> => {
      const results: string[] = [];

      for (const file of files) {
        const url = await uploadFile(file);
        if (url) {
          results.push(url);
        }
      }

      return results;
    },
    [uploadFile],
  );

  return {
    uploadState,
    uploadFile,
    uploadFiles,
    resetUpload,
    isUploading: uploadState.isUploading,
    progress: uploadState.progress,
    error: uploadState.error,
    uploadedUrl: uploadState.uploadedUrl,
    key: uploadState.key,
  };
}
