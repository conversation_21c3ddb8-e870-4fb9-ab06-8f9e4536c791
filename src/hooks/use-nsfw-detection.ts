"use client";

import { use<PERSON><PERSON>back, useEffect, useState } from "react";
import {
  classifyImage,
  classifyImages,
  DEFAULT_NSFW_CONFIG,
  isModelLoaded,
  type NSFWConfig,
  type NSFWDetectionResult,
  preloadNSFWModel,
} from "@/lib/nsfw-detection";

// Hook state interface
interface NSFWDetectionState {
  isLoading: boolean;
  isModelLoaded: boolean;
  error: string | null;
  lastResult: NSFWDetectionResult | null;
}

// Hook options
interface UseNSFWDetectionOptions {
  config?: NSFWConfig;
  preloadModel?: boolean;
  onDetection?: (result: NSFWDetectionResult, file: File) => void;
  onNSFWDetected?: (result: NSFWDetectionResult, file: File) => void;
}

// Hook return type
interface UseNSFWDetectionReturn {
  // State
  isLoading: boolean;
  isModelLoaded: boolean;
  error: string | null;
  lastResult: NSFWDetectionResult | null;

  // Actions
  checkImage: (file: File) => Promise<NSFWDetectionResult>;
  checkImages: (files: File[]) => Promise<NSFWDetectionResult[]>;
  preloadModel: () => Promise<void>;
  clearError: () => void;
}

/**
 * Custom hook for NSFW detection in images
 */
export function useNSFWDetection(
  options: UseNSFWDetectionOptions = {},
): UseNSFWDetectionReturn {
  const {
    config = DEFAULT_NSFW_CONFIG,
    preloadModel: shouldPreload = true,
    onDetection,
    onNSFWDetected,
  } = options;

  const [state, setState] = useState<NSFWDetectionState>({
    isLoading: false,
    isModelLoaded: isModelLoaded(),
    error: null,
    lastResult: null,
  });

  // Preload model on mount if requested
  useEffect(() => {
    if (shouldPreload && !state.isModelLoaded) {
      preloadModelInternal();
    }
  }, [shouldPreload, state.isModelLoaded]);

  // Internal function to preload model
  const preloadModelInternal = useCallback(async () => {
    if (state.isModelLoaded) return;

    setState((prev) => ({ ...prev, isLoading: true, error: null }));

    try {
      await preloadNSFWModel(config);
      setState((prev) => ({
        ...prev,
        isLoading: false,
        isModelLoaded: true,
      }));
    } catch (error) {
      setState((prev) => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : "Failed to load model",
      }));
    }
  }, [config, state.isModelLoaded]);

  // Check a single image
  const checkImage = useCallback(
    async (file: File): Promise<NSFWDetectionResult> => {
      setState((prev) => ({ ...prev, isLoading: true, error: null }));

      try {
        const result = await classifyImage(file, config);

        setState((prev) => ({
          ...prev,
          isLoading: false,
          lastResult: result,
          isModelLoaded: true,
        }));

        // Call callbacks
        onDetection?.(result, file);
        if (result.isNSFW) {
          onNSFWDetected?.(result, file);
        }

        return result;
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Classification failed";

        setState((prev) => ({
          ...prev,
          isLoading: false,
          error: errorMessage,
        }));

        throw error;
      }
    },
    [config, onDetection, onNSFWDetected],
  );

  // Check multiple images
  const checkImages = useCallback(
    async (files: File[]): Promise<NSFWDetectionResult[]> => {
      setState((prev) => ({ ...prev, isLoading: true, error: null }));

      try {
        const results = await classifyImages(files, config);

        setState((prev) => ({
          ...prev,
          isLoading: false,
          isModelLoaded: true,
        }));

        // Call callbacks for each result
        results.forEach((result, index) => {
          const file = files[index];
          if (file) {
            onDetection?.(result, file);
            if (result.isNSFW) {
              onNSFWDetected?.(result, file);
            }
          }
        });

        return results;
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Classification failed";

        setState((prev) => ({
          ...prev,
          isLoading: false,
          error: errorMessage,
        }));

        throw error;
      }
    },
    [config, onDetection, onNSFWDetected],
  );

  // Clear error
  const clearError = useCallback(() => {
    setState((prev) => ({ ...prev, error: null }));
  }, []);

  return {
    // State
    isLoading: state.isLoading,
    isModelLoaded: state.isModelLoaded,
    error: state.error,
    lastResult: state.lastResult,

    // Actions
    checkImage,
    checkImages,
    preloadModel: preloadModelInternal,
    clearError,
  };
}

/**
 * Hook for simple NSFW validation with boolean result
 */
export function useNSFWValidator(options: UseNSFWDetectionOptions = {}): {
  validateImage: (file: File) => Promise<boolean>;
  validateImages: (files: File[]) => Promise<boolean[]>;
  isLoading: boolean;
  error: string | null;
} {
  const { checkImage, checkImages, isLoading, error } =
    useNSFWDetection(options);

  const validateImage = useCallback(
    async (file: File): Promise<boolean> => {
      try {
        const result = await checkImage(file);
        return !result.isNSFW; // Return true if safe, false if NSFW
      } catch {
        return true; // Default to safe if classification fails
      }
    },
    [checkImage],
  );

  const validateImages = useCallback(
    async (files: File[]): Promise<boolean[]> => {
      try {
        const results = await checkImages(files);
        return results.map((result) => !result.isNSFW);
      } catch {
        return files.map(() => true); // Default to safe if classification fails
      }
    },
    [checkImages],
  );

  return {
    validateImage,
    validateImages,
    isLoading,
    error,
  };
}

/**
 * Hook for NSFW detection with automatic filtering
 */
export function useNSFWFilter(options: UseNSFWDetectionOptions = {}): {
  filterImages: (files: File[]) => Promise<File[]>;
  filterResults: NSFWDetectionResult[];
  rejectedFiles: File[];
  isLoading: boolean;
  error: string | null;
} {
  const { checkImages, isLoading, error } = useNSFWDetection(options);
  const [filterResults, setFilterResults] = useState<NSFWDetectionResult[]>([]);
  const [rejectedFiles, setRejectedFiles] = useState<File[]>([]);

  const filterImages = useCallback(
    async (files: File[]): Promise<File[]> => {
      try {
        const results = await checkImages(files);
        setFilterResults(results);

        const safeFiles: File[] = [];
        const rejected: File[] = [];

        results.forEach((result, index) => {
          const file = files[index];
          if (file) {
            if (result.isNSFW) {
              rejected.push(file);
            } else {
              safeFiles.push(file);
            }
          }
        });

        setRejectedFiles(rejected);
        return safeFiles;
      } catch {
        // If classification fails, return all files (fail-safe approach)
        setFilterResults([]);
        setRejectedFiles([]);
        return files;
      }
    },
    [checkImages],
  );

  return {
    filterImages,
    filterResults,
    rejectedFiles,
    isLoading,
    error,
  };
}
