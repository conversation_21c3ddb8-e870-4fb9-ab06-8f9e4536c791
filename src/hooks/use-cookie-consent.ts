"use client";

import { useEffect, useState } from "react";

// Extend Window interface to include PostHog
declare global {
  interface Window {
    posthog?: {
      opt_in_capturing: () => void;
      opt_out_capturing: () => void;
    };
  }
}

export type CookiePreferences = {
  necessary: boolean;
  functional: boolean;
  analytics: boolean;
  marketing: boolean;
};

const COOKIE_CONSENT_KEY = "cookie-consent-preferences";
const COOKIE_CONSENT_VERSION = "1.0";

export function useCookieConsent() {
  const [preferences, setPreferences] = useState<CookiePreferences>({
    necessary: true,
    functional: false,
    analytics: false,
    marketing: false,
  });

  const [loaded, setLoaded] = useState(false);

  useEffect(() => {
    // Only run on client side
    if (typeof window === "undefined") return;

    // Load saved preferences
    const savedPreferences = localStorage.getItem(COOKIE_CONSENT_KEY);

    if (savedPreferences) {
      try {
        const { preferences: savedPrefs } = JSON.parse(savedPreferences);
        setPreferences(savedPrefs);
      } catch (error) {
        console.error("Error parsing saved cookie preferences:", error);
      }
    }

    setLoaded(true);
  }, []);

  // Save preferences to localStorage
  const savePreferences = (newPreferences: CookiePreferences) => {
    // Always ensure necessary cookies are enabled
    const finalPreferences = { ...newPreferences, necessary: true };

    // Save to localStorage
    localStorage.setItem(
      COOKIE_CONSENT_KEY,
      JSON.stringify({
        version: COOKIE_CONSENT_VERSION,
        date: new Date().toISOString(),
        preferences: finalPreferences,
      }),
    );

    setPreferences(finalPreferences);

    // Apply preferences to tracking tools
    applyTrackingPreferences(finalPreferences);

    return finalPreferences;
  };

  // Apply preferences to tracking tools
  const applyTrackingPreferences = (prefs: CookiePreferences) => {
    // PostHog
    if (typeof window !== "undefined" && window.posthog) {
      if (prefs.analytics) {
        window.posthog.opt_in_capturing();
      } else {
        window.posthog.opt_out_capturing();
      }
    }

    // Add other tracking tools here as needed
  };

  // Check if a specific cookie category is allowed
  const isAllowed = (category: keyof CookiePreferences): boolean => {
    return preferences[category];
  };

  // Show the cookie preferences dialog
  const showPreferences = () => {
    // This will be handled by the CookiePreferencesButton component
    // We're just providing a hook for programmatic access
    const event = new CustomEvent("show-cookie-preferences");
    window.dispatchEvent(event);
  };

  return {
    preferences,
    loaded,
    savePreferences,
    isAllowed,
    showPreferences,
  };
}
