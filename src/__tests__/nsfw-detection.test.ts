/**
 * @jest-environment jsdom
 */

import {
  describe,
  it,
  expect,
  beforeEach,
  afterEach,
  jest,
} from "@jest/globals";
import {
  getNSFWConfigForUploadType,
  NSFW_CONFIGS,
} from "@/lib/config/nsfw-config";
import {
  classifyImage,
  classifyImages,
  clearModelCache,
  DEFAULT_NSFW_CONFIG,
  getResultDescription,
  isModelLoaded,
  loadNSFWModel,
  preloadNSFWModel,
} from "@/lib/nsfw-detection";

// Mock TensorFlow.js and NSFWJS
jest.mock("@tensorflow/tfjs", () => ({
  enableProdMode: jest.fn(),
}));

jest.mock("nsfwjs", () => ({
  load: jest.fn(),
}));

// Mock file creation helper
function createMockImageFile(name = "test.jpg", type = "image/jpeg"): File {
  const blob = new Blob(["fake image data"], { type });
  return new File([blob], name, { type });
}

// Mock NSFW predictions
const mockSafePredictions = [
  { className: "Drawing", probability: 0.8 },
  { className: "Neutral", probability: 0.15 },
  { className: "Sexy", probability: 0.03 },
  { className: "Porn", probability: 0.01 },
  { className: "Hentai", probability: 0.01 },
];

const mockNSFWPredictions = [
  { className: "Porn", probability: 0.85 },
  { className: "Sexy", probability: 0.1 },
  { className: "Neutral", probability: 0.03 },
  { className: "Drawing", probability: 0.01 },
  { className: "Hentai", probability: 0.01 },
];

const mockModel = {
  classify: jest.fn(),
  model: {
    dispose: jest.fn(),
  },
};

describe("NSFW Detection", () => {
  beforeEach(() => {
    // Clear any cached model before each test
    clearModelCache();
    jest.clearAllMocks();

    // Mock the NSFWJS load function
    const nsfwjs = require("nsfwjs");
    nsfwjs.load.mockResolvedValue(mockModel);
  });

  afterEach(() => {
    clearModelCache();
  });

  describe("Model Loading", () => {
    it("should load the NSFW model successfully", async () => {
      const model = await loadNSFWModel();
      expect(model).toBeDefined();
      expect(isModelLoaded()).toBe(true);
    });

    it("should cache the model after first load", async () => {
      const nsfwjs = require("nsfwjs");

      // Load model twice
      await loadNSFWModel();
      await loadNSFWModel();

      // Should only call load once due to caching
      expect(nsfwjs.load).toHaveBeenCalledTimes(1);
    });

    it("should preload model without errors", async () => {
      await expect(preloadNSFWModel()).resolves.not.toThrow();
      expect(isModelLoaded()).toBe(true);
    });

    it("should handle model loading errors", async () => {
      const nsfwjs = require("nsfwjs");
      nsfwjs.load.mockRejectedValue(new Error("Model load failed"));

      await expect(loadNSFWModel()).rejects.toThrow(
        "Failed to load NSFW model",
      );
    });
  });

  describe("Image Classification", () => {
    beforeEach(async () => {
      // Ensure model is loaded for classification tests
      await loadNSFWModel();
    });

    it("should classify safe image correctly", async () => {
      mockModel.classify.mockResolvedValue(mockSafePredictions);

      const file = createMockImageFile();
      const result = await classifyImage(file);

      expect(result.isNSFW).toBe(false);
      expect(result.predictions).toEqual(mockSafePredictions);
      expect(result.flaggedCategory).toBeNull();
    });

    it("should classify NSFW image correctly", async () => {
      mockModel.classify.mockResolvedValue(mockNSFWPredictions);

      const file = createMockImageFile();
      const result = await classifyImage(file, {
        ...DEFAULT_NSFW_CONFIG,
        threshold: 0.6,
        flaggedCategories: ["Porn", "Hentai"],
      });

      expect(result.isNSFW).toBe(true);
      expect(result.predictions).toEqual(mockNSFWPredictions);
      expect(result.flaggedCategory).toBe("Porn");
    });

    it("should respect threshold configuration", async () => {
      mockModel.classify.mockResolvedValue([
        { className: "Porn", probability: 0.5 },
        { className: "Neutral", probability: 0.5 },
      ]);

      const file = createMockImageFile();

      // With high threshold, should be safe
      const resultHigh = await classifyImage(file, {
        ...DEFAULT_NSFW_CONFIG,
        threshold: 0.8,
      });
      expect(resultHigh.isNSFW).toBe(false);

      // With low threshold, should be flagged
      const resultLow = await classifyImage(file, {
        ...DEFAULT_NSFW_CONFIG,
        threshold: 0.3,
      });
      expect(resultLow.isNSFW).toBe(true);
    });

    it("should handle non-image files", async () => {
      const file = new File(["text"], "test.txt", { type: "text/plain" });

      await expect(classifyImage(file)).rejects.toThrow(
        "File must be an image",
      );
    });

    it("should classify multiple images", async () => {
      mockModel.classify
        .mockResolvedValueOnce(mockSafePredictions)
        .mockResolvedValueOnce(mockNSFWPredictions);

      const files = [
        createMockImageFile("safe.jpg"),
        createMockImageFile("nsfw.jpg"),
      ];

      const results = await classifyImages(files);

      expect(results).toHaveLength(2);
      expect(results[0].isNSFW).toBe(false);
      expect(results[1].isNSFW).toBe(true);
    });
  });

  describe("Configuration", () => {
    it("should use correct config for upload types", () => {
      const propertyConfig = getNSFWConfigForUploadType("property-image");
      const jobConfig = getNSFWConfigForUploadType("job-image");
      const profileConfig = getNSFWConfigForUploadType("profile-image");

      expect(propertyConfig).toEqual(NSFW_CONFIGS.strict);
      expect(jobConfig).toEqual(NSFW_CONFIGS.moderate);
      expect(profileConfig).toEqual(NSFW_CONFIGS.strict);
    });

    it("should have different threshold levels", () => {
      expect(NSFW_CONFIGS.strict.threshold).toBeLessThan(
        NSFW_CONFIGS.moderate.threshold,
      );
      expect(NSFW_CONFIGS.moderate.threshold).toBeLessThan(
        NSFW_CONFIGS.lenient.threshold,
      );
    });

    it("should have appropriate flagged categories", () => {
      expect(NSFW_CONFIGS.strict.flaggedCategories).toContain("Porn");
      expect(NSFW_CONFIGS.strict.flaggedCategories).toContain("Hentai");
      expect(NSFW_CONFIGS.strict.flaggedCategories).toContain("Sexy");

      expect(NSFW_CONFIGS.lenient.flaggedCategories).toContain("Porn");
      expect(NSFW_CONFIGS.lenient.flaggedCategories).not.toContain("Sexy");
    });
  });

  describe("Result Processing", () => {
    it("should generate appropriate descriptions for safe content", () => {
      const safeResult = {
        isNSFW: false,
        predictions: mockSafePredictions,
        highestProbability: 0.8,
        flaggedCategory: null,
      };

      const description = getResultDescription(safeResult);
      expect(description).toContain("safe for work");
    });

    it("should generate appropriate descriptions for NSFW content", () => {
      const nsfwResult = {
        isNSFW: true,
        predictions: mockNSFWPredictions,
        highestProbability: 0.85,
        flaggedCategory: "Porn",
      };

      const description = getResultDescription(nsfwResult);
      expect(description).toContain("inappropriate content");
      expect(description).toContain("Porn");
      expect(description).toContain("85%");
    });
  });

  describe("Memory Management", () => {
    it("should clear model cache", () => {
      clearModelCache();
      expect(isModelLoaded()).toBe(false);
    });

    it("should dispose of model resources when clearing cache", async () => {
      await loadNSFWModel();
      clearModelCache();

      expect(mockModel.model.dispose).toHaveBeenCalled();
    });
  });

  describe("Error Handling", () => {
    it("should handle classification errors gracefully", async () => {
      await loadNSFWModel();
      mockModel.classify.mockRejectedValue(new Error("Classification failed"));

      const file = createMockImageFile();
      await expect(classifyImage(file)).rejects.toThrow(
        "NSFW classification failed",
      );
    });

    it("should handle batch classification errors", async () => {
      await loadNSFWModel();
      mockModel.classify
        .mockResolvedValueOnce(mockSafePredictions)
        .mockRejectedValueOnce(new Error("Classification failed"));

      const files = [
        createMockImageFile("safe.jpg"),
        createMockImageFile("error.jpg"),
      ];

      const results = await classifyImages(files);

      expect(results).toHaveLength(2);
      expect(results[0].isNSFW).toBe(false);
      expect(results[1].isNSFW).toBe(false); // Should default to safe on error
    });
  });
});

// Integration test to verify the upload flow works with NSFW detection
describe("NSFW Detection Integration", () => {
  it("should integrate with upload flow", async () => {
    // This would be an integration test that verifies:
    // 1. Image is selected
    // 2. NSFW detection runs
    // 3. Safe images proceed to upload
    // 4. NSFW images are blocked
    // 5. User receives appropriate feedback

    // Note: This would require setting up React Testing Library
    // and mocking the upload components
    expect(true).toBe(true); // Placeholder
  });
});
