# Component Consolidation Plan

## 1. Duplicate Components with Enhanced Versions

| Original Component | Enhanced Version | Action |
|-------------------|------------------|--------|
| `src/components/features/dashboard/dashboard.tsx` | `src/components/features/dashboard/enhanced-dashboard.tsx` | Replace original with enhanced version and rename to `dashboard.tsx` |
| `src/components/features/chat/jack-introduction.tsx` | `src/components/features/chat/enhanced-jack-introduction.tsx` | Replace original with enhanced version and rename to `jack-introduction.tsx` |
| `src/components/features/contractors/crew-settings.tsx` | `src/components/features/contractors/enhanced-contractor-settings.tsx` | Replace original with enhanced version and rename to `contractor-settings.tsx` |
| `src/components/features/tours/job-wizard-tour.tsx` (implied) | `src/components/features/tours/enhanced-job-wizard-tour.tsx` | Keep enhanced version and rename to `job-wizard-tour.tsx` |

## 2. Components with "enhanced" in Export Names

The following components have "enhanced" in their export names but not in the file names:
- `EnhancedPopupChat` from `src/components/features/chat/enhanced-popup-chat.tsx`
- `EnhancedPusherChat` from `src/components/features/chat/enhanced-pusher-chat.tsx`
- `EnhancedOnboarding` from `src/components/features/onboarding/enhanced-onboarding.tsx`
- `EnhancedPropertyImageUploader` from `src/components/features/properties/enhanced-property-image-uploader.tsx`

Action: Rename exports to remove "Enhanced" prefix and update all imports.

## 3. Mentioned in Documentation but Not Found

The following components are mentioned in documentation but weren't found in the file system:
- `src/components/projects/enhanced-project-detail-content.tsx`
- `src/components/projects/enhanced-project-actions.tsx`

Action: Verify if these files exist and if they should be created or if the documentation is outdated.

## 4. Implementation Plan

1. For each duplicate component:
   - Verify that the enhanced version is a complete replacement for the original
   - Update all imports to point to the enhanced version
   - Rename the enhanced version to the original name
   - Remove the original component

2. For components with "enhanced" in export names:
   - Update the export name to remove "Enhanced" prefix
   - Update all imports to use the new export name

3. For components mentioned in documentation but not found:
   - Verify if these files should exist
   - Create them if needed or update documentation

4. Update index.ts files to export the consolidated components