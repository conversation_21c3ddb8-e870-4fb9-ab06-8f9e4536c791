CREATE TYPE "public"."waitlist_status" AS ENUM('PENDING', 'APPROVED', 'REJECTED', 'INVITED');--> statement-breakpoint
CREATE TABLE "waitlist" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"email" text NOT NULL,
	"reason" text,
	"accountType" text NOT NULL,
	"status" "waitlist_status" DEFAULT 'PENDING' NOT NULL,
	"createdAt" timestamp DEFAULT now() NOT NULL,
	"updatedAt" timestamp DEFAULT now() NOT NULL,
	"invitedAt" timestamp,
	"inviteToken" text,
	"inviteExpires" timestamp,
	CONSTRAINT "waitlist_email_unique" UNIQUE("email")
);
--> statement-breakpoint
ALTER TABLE "property" ALTER COLUMN "imageUrl" DROP NOT NULL;--> statement-breakpoint
ALTER TABLE "organization" ADD COLUMN "verified" boolean DEFAULT false NOT NULL;--> statement-breakpoint
ALTER TABLE "property" ADD COLUMN "type" text;--> statement-breakpoint
ALTER TABLE "property" ADD COLUMN "description" text;