# NSFW Detection Implementation

This document describes the NSFW (Not Safe For Work) content detection system implemented in the TradeCrews application.

## Overview

The NSFW detection system uses TensorFlow.js and the NSFWJS library to analyze images client-side before they are uploaded. This helps ensure that inappropriate content is filtered out automatically, maintaining a professional environment on the platform.

## Features

- **Client-side Detection**: All image analysis happens in the browser - no images are sent to external servers
- **Multiple Sensitivity Levels**: Configurable thresholds for different contexts (strict, moderate, lenient)
- **Real-time Feedback**: Users receive immediate feedback about content appropriateness
- **Seamless Integration**: Works with existing Cloudflare upload components
- **Performance Optimized**: Model caching and lazy loading for optimal performance

## Architecture

### Core Components

1. **NSFW Detection Library** (`src/lib/nsfw-detection.ts`)
   - Core functionality for loading models and classifying images
   - Handles TensorFlow.js model management
   - Provides result processing and caching

2. **React Hooks** (`src/hooks/use-nsfw-detection.ts`)
   - `useNSFWDetection`: Main hook for NSFW detection
   - `useNSFWValidator`: Simple boolean validation
   - `useNSFWFilter`: Automatic filtering of image arrays

3. **Configuration System** (`src/lib/config/nsfw-config.ts`)
   - Predefined sensitivity levels
   - Upload-type specific configurations
   - Environment and role-based settings

4. **User Feedback Components** (`src/components/features/upload/nsfw-feedback.tsx`)
   - `NSFWFeedback`: Full feedback component with details
   - `NSFWFeedbackCompact`: Compact inline feedback
   - `NSFWStatusIndicator`: Simple status icon

### Integration Points

- **CloudflareSingleImageUploader**: Updated with NSFW detection
- **CloudflareImageUploader**: Multi-image upload with filtering
- **Upload Flow**: Seamless integration with existing upload process

## Configuration

### Sensitivity Levels

```typescript
// Strict - for public-facing content (property images, profiles)
{
  threshold: 0.4,
  flaggedCategories: ["Porn", "Hentai", "Sexy"],
  modelType: "MobileNetV2"
}

// Moderate - for general use (job images)
{
  threshold: 0.6,
  flaggedCategories: ["Porn", "Hentai"],
  modelType: "MobileNetV2"
}

// Lenient - for less restrictive contexts
{
  threshold: 0.8,
  flaggedCategories: ["Porn"],
  modelType: "MobileNetV2"
}
```

### Upload Type Configuration

- **Property Images**: Strict filtering (family-friendly)
- **Job Images**: Moderate filtering (professional context)
- **Profile Images**: Strict filtering (public-facing)

## Usage

### Basic Implementation

```tsx
import { CloudflareSingleImageUploader } from "@/components/features/upload/cloudflare-single-image-uploader";

function MyComponent() {
  const [imageUrl, setImageUrl] = useState<string | null>(null);

  return (
    <CloudflareSingleImageUploader
      value={imageUrl}
      onChange={setImageUrl}
      uploadType="job-image"
      enableNSFWDetection={true} // Default: true
    />
  );
}
```

### Advanced Usage with Custom Configuration

```tsx
import { useNSFWDetection } from "@/hooks/use-nsfw-detection";
import { NSFW_CONFIGS } from "@/lib/config/nsfw-config";

function AdvancedUploader() {
  const { checkImage, isLoading, error } = useNSFWDetection({
    config: NSFW_CONFIGS.strict,
    onNSFWDetected: (result, file) => {
      console.log("NSFW content detected:", result);
    },
  });

  const handleFileSelect = async (file: File) => {
    const result = await checkImage(file);
    if (!result.isNSFW) {
      // Proceed with upload
    }
  };

  return (
    // Your upload UI
  );
}
```

### Manual Detection

```tsx
import { classifyImage } from "@/lib/nsfw-detection";

async function checkImageManually(file: File) {
  try {
    const result = await classifyImage(file, {
      threshold: 0.6,
      flaggedCategories: ["Porn", "Hentai"],
      modelType: "MobileNetV2"
    });
    
    console.log("Is NSFW:", result.isNSFW);
    console.log("Predictions:", result.predictions);
  } catch (error) {
    console.error("Detection failed:", error);
  }
}
```

## Performance Considerations

### Model Loading

- **Size**: ~50MB download on first use
- **Caching**: Model is cached in browser memory
- **Preloading**: Can be preloaded on app initialization or page navigation

### Optimization Strategies

1. **Lazy Loading**: Model loads only when needed
2. **Memory Management**: Proper cleanup of TensorFlow.js tensors
3. **Batch Processing**: Efficient handling of multiple images
4. **Error Handling**: Graceful fallbacks when detection fails

## Testing

### Unit Tests

Run the NSFW detection tests:

```bash
npm test -- src/__tests__/nsfw-detection.test.ts
```

### Integration Testing

Use the demo page to test functionality:

```bash
npm run dev
# Visit: http://localhost:3000/(demo)/nsfw-demo
```

### Test Script

Run the comprehensive test script:

```bash
node scripts/test-nsfw.js
```

## Error Handling

The system includes comprehensive error handling:

1. **Model Loading Failures**: Graceful fallback with user notification
2. **Classification Errors**: Default to safe classification
3. **Network Issues**: Offline capability with cached models
4. **Invalid Files**: Proper validation and user feedback

## Security Considerations

- **Client-side Only**: No images sent to external servers
- **Privacy Preserving**: All analysis happens locally
- **Fail-safe Design**: Defaults to allowing content if detection fails
- **User Control**: Users can disable detection if needed

## Browser Compatibility

- **Modern Browsers**: Chrome 76+, Firefox 78+, Safari 14+, Edge 79+
- **WebAssembly**: Required for TensorFlow.js
- **Memory**: Minimum 2GB RAM recommended for optimal performance

## Troubleshooting

### Common Issues

1. **Model Won't Load**
   - Check browser compatibility
   - Verify network connection
   - Clear browser cache

2. **Slow Performance**
   - Ensure sufficient memory
   - Check for other resource-intensive tabs
   - Consider using MobileNetV2 instead of InceptionV3

3. **False Positives**
   - Adjust threshold settings
   - Review flagged categories
   - Consider using lenient configuration

### Debug Mode

Enable debug logging:

```typescript
// In development
localStorage.setItem('nsfw-debug', 'true');
```

## Future Enhancements

- **Custom Models**: Support for domain-specific models
- **Batch Optimization**: Improved performance for multiple images
- **Progressive Enhancement**: Better fallbacks for older browsers
- **Analytics**: Usage metrics and accuracy tracking

## Dependencies

- `nsfwjs`: ^4.2.1
- `@tensorflow/tfjs`: ^4.22.0

## License

This implementation uses the NSFWJS library which is licensed under MIT. The models are trained on publicly available datasets and are suitable for commercial use.
