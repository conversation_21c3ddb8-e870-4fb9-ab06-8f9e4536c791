# Waitlist Modal Troubleshooting Guide

This guide helps debug issues with the waitlist modal not appearing when clicking sign-up links.

## Quick Debugging Steps

### 1. Add Debug Components

Add these components to your page to debug the issue:

```tsx
// Add to any page for testing
import { WaitlistTest } from "@/components/debug/waitlist-test";
import { PostHogDebug } from "@/components/debug/posthog-debug";

export default function TestPage() {
  return (
    <div>
      <WaitlistTest />
      <PostHogDebug />
    </div>
  );
}
```

### 2. Check Browser Console

Open browser dev tools and look for these debug messages:

```
Waitlist Debug: {
  standardWaitlistEnabled: false,
  anonymousWaitlistEnabled: false,
  finalWaitlistEnabled: false,
  isAnonymousTrackingReady: true,
  flagName: "enable-waitlist"
}
```

### 3. Test Feature Flag

Use the debug component to check the feature flag:
- Click "Check Flag" button
- Should show `true` if waitlist is enabled

## Common Issues & Solutions

### Issue 1: Feature Flag Not Enabled

**Symptoms:**
- Console shows `finalWaitlistEnabled: false`
- Debug component shows "Waitlist Enabled: No"

**Solutions:**

1. **Check PostHog Feature Flag Configuration:**
   - Go to PostHog dashboard
   - Navigate to Feature Flags
   - Find `enable-waitlist` flag
   - Ensure it's enabled for your environment
   - Check rollout percentage (should be 100% for testing)

2. **Enable for Anonymous Users:**
   - In PostHog flag settings, ensure "Release conditions" include anonymous users
   - Or set rollout to 100% for all users

3. **Force Enable for Development:**
   ```tsx
   // Temporary override for testing
   const waitlistEnabled = true; // Force enable
   ```

### Issue 2: Anonymous Tracking Not Ready

**Symptoms:**
- Console shows `isAnonymousTrackingReady: false`
- Debug component shows "Tracking Ready: No"

**Solutions:**

1. **Check PostHog Configuration:**
   ```tsx
   // Verify env variables are set
   console.log({
     NEXT_PUBLIC_POSTHOG_KEY: process.env.NEXT_PUBLIC_POSTHOG_KEY,
     NEXT_PUBLIC_POSTHOG_HOST: process.env.NEXT_PUBLIC_POSTHOG_HOST,
   });
   ```

2. **Check Cookie Creation:**
   - Open browser dev tools → Application → Cookies
   - Look for `tc_anonymous_id` cookie
   - Should contain a UUID value

3. **Check PostHog Cookie:**
   - Look for `ph_{PROJECT_KEY}_posthog` cookie
   - Should contain PostHog session data

### Issue 3: Link Click Handler Not Working

**Symptoms:**
- Console shows "Waitlist link handler not active - waitlist disabled"
- OR no console messages when clicking links

**Solutions:**

1. **Check Link Format:**
   Links must contain `/sign-up` in the href:
   ```tsx
   // ✅ These will work
   <a href="/sign-up">Sign Up</a>
   <a href="/sign-up/homeowner">Homeowner Sign Up</a>
   <a href="/sign-up/contractor">Contractor Sign Up</a>
   
   // ❌ These won't work
   <a href="/signup">Sign Up</a>
   <a href="/register">Register</a>
   ```

2. **Check Event Listener:**
   - The click handler is added to `document`
   - Should see console message: "Link clicked: { href: '/sign-up', isSignUpLink: true }"

3. **Test Direct Modal Call:**
   ```tsx
   // Test if modal works directly
   const { showWaitlistModal } = useWaitlist();
   showWaitlistModal("homeowner");
   ```

### Issue 4: Modal Opens But Doesn't Show

**Symptoms:**
- Console shows "Showing waitlist modal for type: homeowner"
- But modal is not visible

**Solutions:**

1. **Check Modal State:**
   ```tsx
   // Add to WaitlistProvider for debugging
   console.log("Modal state:", { isModalOpen, accountType });
   ```

2. **Check CSS/Z-index Issues:**
   - Modal might be behind other elements
   - Check if modal backdrop is visible
   - Inspect modal element in dev tools

3. **Check for JavaScript Errors:**
   - Look for errors in console that might prevent modal rendering
   - Check if WaitlistModal component is properly imported

## Testing Checklist

Use this checklist to verify everything is working:

- [ ] PostHog is initialized and working
- [ ] Anonymous ID is created and stored in cookie
- [ ] Feature flag `enable-waitlist` is enabled in PostHog
- [ ] Feature flag evaluates to `true` for anonymous users
- [ ] WaitlistProvider is wrapping your app
- [ ] Sign-up links contain `/sign-up` in href
- [ ] Click handler is attached to document
- [ ] Modal state updates when showWaitlistModal is called
- [ ] WaitlistModal component renders without errors

## Manual Testing

1. **Test Feature Flag:**
   ```tsx
   const { isFeatureEnabled } = usePostHogAnonymous();
   console.log("Waitlist flag:", isFeatureEnabled("enable-waitlist"));
   ```

2. **Test Modal Directly:**
   ```tsx
   const { showWaitlistModal } = useWaitlist();
   showWaitlistModal("homeowner");
   ```

3. **Test Link Click:**
   - Create a test link: `<a href="/sign-up/homeowner">Test</a>`
   - Click it and check console for debug messages

## Environment Variables

Ensure these are set correctly:

```env
NEXT_PUBLIC_POSTHOG_KEY=phc_your_project_key
NEXT_PUBLIC_POSTHOG_HOST=https://us.posthog.com
```

## PostHog Dashboard Checks

1. **Feature Flags:**
   - Go to Feature Flags section
   - Find `enable-waitlist`
   - Check it's enabled and has proper rollout

2. **Events:**
   - Go to Events section
   - Look for `waitlist_modal_shown` events
   - Verify anonymous users are tracked

3. **Persons:**
   - Check if anonymous users appear in persons list
   - Verify they have the anonymous ID property

## Still Not Working?

If the modal still doesn't appear:

1. **Simplify the test:**
   ```tsx
   // Add this button to test directly
   <button onClick={() => showWaitlistModal("homeowner")}>
     Test Waitlist Modal
   </button>
   ```

2. **Check network requests:**
   - Open Network tab in dev tools
   - Look for PostHog API calls
   - Verify feature flag requests are successful

3. **Try incognito mode:**
   - Test in private/incognito browser window
   - This ensures clean state without cached data

4. **Check for conflicts:**
   - Temporarily disable other click handlers
   - Check if other libraries are interfering

## Getting Help

When reporting issues, include:

1. Browser console output (especially debug messages)
2. Network tab showing PostHog requests
3. Cookie values (`tc_anonymous_id` and PostHog cookie)
4. Feature flag configuration from PostHog dashboard
5. Code snippet of how you're implementing the waitlist
